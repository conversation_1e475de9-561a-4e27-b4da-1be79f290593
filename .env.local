
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://qwgffjjkcicexwqnhvqw.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3Z2ZmamprY2ljZXh3cW5odnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2ODgyNjAsImV4cCI6MjA2MDI2NDI2MH0.UCq6jp8EPCvp3cmuvR5bKVykebpP6AWIjsRB6AZPuZU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3Z2ZmamprY2ljZXh3cW5odnF3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDY4ODI2MCwiZXhwIjoyMDYwMjY0MjYwfQ.1g1UUXDh8zeZqgrr4SiCn8CMkKsFdN2jpCpBaJF7ZCE

# OpenRouter API Configuration
OPENROUTER_API_KEY=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

NEXT_PUBLIC_OPENROUTER_BASE_URL=https://openrouter.ai/api/v1



# Admin Security Configuration
ADMIN_SETUP_PASSWORD=yAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ
ADMIN_SETUP_KEY=f62c8e2bf4952eebe5c4c

# Firebase Configuration
# NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ
# NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=zatconss.firebaseapp.com
# NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://zatconss-default-rtdb.firebaseio.com
# NEXT_PUBLIC_FIREBASE_PROJECT_ID=zatconss
# NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=zatconss.firebasestorage.app
# NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=947257597349
# NEXT_PUBLIC_FIREBASE_APP_ID=1:947257597349:web:4f62c8e2bf4952eebe5c4c
# NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-ZCHBDYX3VW

