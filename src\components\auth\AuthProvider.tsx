'use client'

import { useEffect } from 'react'
import { onAuthStateChanged } from 'firebase/auth'
import { doc, getDoc, setDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { useAuthStore } from '@/lib/store'
import { User } from '@/lib/types'

interface AuthProviderProps {
  children: React.ReactNode
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const { setUser, setLoading } = useAuthStore()

  useEffect(() => {
    console.log('AuthProvider: Setting up auth state listener')
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log('AuthProvider: Auth state changed:', firebaseUser ? firebaseUser.uid : 'null')
      setLoading(true)

      if (firebaseUser) {
        try {
          // Get user document from Firestore
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))

          if (userDoc.exists()) {
            const userData = userDoc.data() as User
            console.log('AuthProvider: User document found, setting user:', userData.id)
            setUser(userData)
          } else {
            // User exists in Firebase Auth but not in Firestore
            // Create the user document automatically
            console.log('Creating missing user document for:', firebaseUser.uid)

            const newUser: User = {
              id: firebaseUser.uid,
              email: firebaseUser.email || '',
              displayName: firebaseUser.displayName || '',
              photoURL: firebaseUser.photoURL || undefined,
              role: 'user', // Default role
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now(),
              preferences: {
                theme: 'light',
                notifications: true
              }
            }

            try {
              await setDoc(doc(db, 'users', firebaseUser.uid), newUser)
              setUser(newUser)
              console.log('User document created successfully')
            } catch (error) {
              console.error('Error creating user document:', error)
              setUser(null)
            }
          }
        } catch (error) {
          console.error('Error fetching user data:', error)
          setUser(null)
        }
      } else {
        setUser(null)
      }
      setLoading(false)
    })

    return () => unsubscribe()
  }, [setUser, setLoading])

  return <>{children}</>
}
