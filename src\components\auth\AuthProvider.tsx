'use client'

import { useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import { User } from '@/lib/types'

interface AuthProviderProps {
  children: React.ReactNode
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const { setUser, setLoading } = useAuthStore()

  useEffect(() => {
    const supabase = createSupabaseClient()

    // Get initial session
    const getInitialSession = async () => {
      setLoading(true)

      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (session?.user) {
          // Get user profile from our custom table
          const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('Error fetching user profile:', error)
            setUser(null)
          } else if (profile) {
            setUser(profile as User)
          } else {
            // Profile doesn't exist, it should be created by the trigger
            // But let's create it manually if needed
            const newProfile = {
              id: session.user.id,
              email: session.user.email || '',
              display_name: session.user.user_metadata?.display_name || session.user.email || '',
              photo_url: session.user.user_metadata?.avatar_url,
              role: 'user' as const,
              preferences: {
                theme: 'light' as const,
                notifications: true
              }
            }

            const { data: createdProfile, error: createError } = await supabase
              .from('user_profiles')
              .insert([newProfile])
              .select()
              .single()

            if (createError) {
              console.error('Error creating user profile:', createError)
              setUser(null)
            } else {
              setUser(createdProfile as User)
            }
          }
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error('Error in auth provider:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      setLoading(true)

      if (session?.user) {
        // Get user profile
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', session.user.id)
          .single()

        setUser(profile as User)
      } else {
        setUser(null)
      }

      setLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [setUser, setLoading])

  return <>{children}</>
}
