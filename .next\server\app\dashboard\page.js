/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/AuthProvider.tsx */ \"(ssr)/./src/components/auth/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NvZGUtZmlsZXMlNUMlNUNhaSUyMGZpbGVzJTVDJTVDdG8tZG8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXV0aCU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcm9kdWN0aXZpdHktcGxhdGZvcm0vP2YwOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcY29kZS1maWxlc1xcXFxhaSBmaWxlc1xcXFx0by1kb1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXEF1dGhQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFtRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByb2R1Y3Rpdml0eS1wbGF0Zm9ybS8/OTNkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNvZGUtZmlsZXNcXFxcYWkgZmlsZXNcXFxcdG8tZG9cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,ClockIcon,FolderIcon,ListBulletIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Mock data for demonstration\nconst mockStats = {\n    totalProjects: 5,\n    totalTasks: 23,\n    completedTasks: 15,\n    upcomingDeadlines: 3\n};\nconst mockRecentTasks = [\n    {\n        id: \"1\",\n        title: \"Design new landing page\",\n        project: \"Website Redesign\",\n        priority: \"high\",\n        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n        status: \"in-progress\"\n    },\n    {\n        id: \"2\",\n        title: \"Review user feedback\",\n        project: \"Product Research\",\n        priority: \"medium\",\n        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),\n        status: \"todo\"\n    },\n    {\n        id: \"3\",\n        title: \"Update documentation\",\n        project: \"API Development\",\n        priority: \"low\",\n        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n        status: \"review\"\n    }\n];\nconst mockUpcomingEvents = [\n    {\n        id: \"1\",\n        title: \"Team Standup\",\n        time: \"9:00 AM\",\n        date: \"Today\"\n    },\n    {\n        id: \"2\",\n        title: \"Client Review Meeting\",\n        time: \"2:00 PM\",\n        date: \"Tomorrow\"\n    },\n    {\n        id: \"3\",\n        title: \"Sprint Planning\",\n        time: \"10:00 AM\",\n        date: \"Friday\"\n    }\n];\nfunction DashboardPage() {\n    const { projects } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useProjectStore)();\n    const { tasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useTaskStore)();\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n            case \"urgent\":\n                return \"text-red-600 bg-red-100\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"low\":\n                return \"text-green-600 bg-green-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"done\":\n                return \"text-green-600 bg-green-100\";\n            case \"in-progress\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"review\":\n                return \"text-purple-600 bg-purple-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Good morning! \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Here's what's happening with your projects today.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/projects/new\",\n                                className: \"btn-primary flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Project\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-blue-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Projects\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: mockStats.totalProjects\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-green-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Tasks\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: mockStats.totalTasks\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-purple-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-6 w-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: mockStats.completedTasks\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-red-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Due Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: mockStats.upcomingDeadlines\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Recent Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        href: \"/tasks\",\n                                                        className: \"text-sm text-primary-600 hover:text-primary-700\",\n                                                        children: \"View all\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: mockRecentTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: task.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: task.project\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(task.priority)}`,\n                                                                                children: task.priority\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 195,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`,\n                                                                                children: task.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 198,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"Due \",\n                                                                        task.dueDate.toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, task.id, true, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Quick Todo\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-sm text-gray-700\",\n                                                                        children: \"Review project proposals\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-sm text-gray-700\",\n                                                                        children: \"Update team on progress\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\",\n                                                                        defaultChecked: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-sm text-gray-700 line-through\",\n                                                                        children: \"Prepare presentation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full mt-4 text-sm text-primary-600 hover:text-primary-700 border border-dashed border-primary-300 rounded-lg py-2\",\n                                                        children: \"+ Add new item\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: \"Upcoming Events\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_ClockIcon_FolderIcon_ListBulletIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: mockUpcomingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: event.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 257,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-600\",\n                                                                                children: [\n                                                                                    event.date,\n                                                                                    \" at \",\n                                                                                    event.time\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, event.id, true, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        href: \"/calendar\",\n                                                        className: \"block w-full mt-4 text-center text-sm text-primary-600 hover:text-primary-700 border border-dashed border-primary-300 rounded-lg py-2\",\n                                                        children: \"View Calendar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AuthProvider({ children }) {\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthProvider: Setting up auth state listener\");\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (firebaseUser)=>{\n            console.log(\"AuthProvider: Auth state changed:\", firebaseUser ? firebaseUser.uid : \"null\");\n            setLoading(true);\n            if (firebaseUser) {\n                try {\n                    // Get user document from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        console.log(\"AuthProvider: User document found, setting user:\", userData.id);\n                        setUser(userData);\n                    } else {\n                        // User exists in Firebase Auth but not in Firestore\n                        // Create the user document automatically\n                        console.log(\"Creating missing user document for:\", firebaseUser.uid);\n                        const newUser = {\n                            id: firebaseUser.uid,\n                            email: firebaseUser.email || \"\",\n                            displayName: firebaseUser.displayName || \"\",\n                            photoURL: firebaseUser.photoURL || undefined,\n                            role: \"user\",\n                            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            preferences: {\n                                theme: \"light\",\n                                notifications: true\n                            }\n                        };\n                        try {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid), newUser);\n                            setUser(newUser);\n                            console.log(\"User document created successfully\");\n                        } catch (error) {\n                            console.error(\"Error creating user document:\", error);\n                            setUser(null);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user data:\", error);\n                    setUser(null);\n                }\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProtectedRoute({ children, requireAdmin = false, redirectTo = \"/auth/login\" }) {\n    const { user, loading, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [authChecked, setAuthChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_4__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth, (firebaseUser)=>{\n            if (!firebaseUser) {\n                setLoading(false);\n                setAuthChecked(true);\n                router.push(redirectTo);\n                return;\n            }\n            setAuthChecked(true);\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, [\n        router,\n        redirectTo,\n        setLoading\n    ]);\n    // Show loading while checking authentication\n    if (loading || !authChecked) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        router.push(redirectTo);\n        return null;\n    }\n    // Check admin requirement\n    if (requireAdmin && user.role !== \"admin\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"You need admin privileges to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                        children: \"Go to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/ChatPanel.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/ChatPanel.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,PaperAirplaneIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ChatPanel() {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hello! I'm your AI assistant. I can help you create tasks, schedule events, manage your projects, and much more. What would you like to work on today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const { setChatPanelOpen } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useUIStore)();\n    const { isLoading, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useChatStore)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!message.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: message,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage(\"\");\n        setLoading(true);\n        // TODO: Implement actual AI API call\n        setTimeout(()=>{\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I understand you want to work on that. Let me help you create a task for this.\",\n                timestamp: new Date(),\n                actions: [\n                    {\n                        type: \"create_task\",\n                        data: {\n                            title: \"Sample Task\",\n                            description: \"This is a sample task based on your request\",\n                            priority: \"medium\"\n                        }\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n            setLoading(false);\n        }, 1000);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleApproveAction = (messageId, actionIndex)=>{\n        setMessages((prev)=>prev.map((msg)=>{\n                if (msg.id === messageId && msg.actions) {\n                    const updatedActions = [\n                        ...msg.actions\n                    ];\n                    updatedActions[actionIndex] = {\n                        ...updatedActions[actionIndex],\n                        approved: true\n                    };\n                    return {\n                        ...msg,\n                        actions: updatedActions\n                    };\n                }\n                return msg;\n            }));\n    };\n    const handleRejectAction = (messageId, actionIndex)=>{\n        setMessages((prev)=>prev.map((msg)=>{\n                if (msg.id === messageId && msg.actions) {\n                    const updatedActions = [\n                        ...msg.actions\n                    ];\n                    updatedActions[actionIndex] = {\n                        ...updatedActions[actionIndex],\n                        approved: false\n                    };\n                    return {\n                        ...msg,\n                        actions: updatedActions\n                    };\n                }\n                return msg;\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-white flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: \"AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setChatPanelOpen(false),\n                        className: \"p-1 rounded hover:bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-5 w-5 text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `max-w-[80%] ${msg.role === \"user\" ? \"order-2\" : \"order-1\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-start space-x-2 ${msg.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${msg.role === \"user\" ? \"bg-primary-600\" : \"bg-gray-200\"}`,\n                                                children: msg.role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `rounded-lg px-3 py-2 ${msg.role === \"user\" ? \"bg-primary-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm whitespace-pre-wrap\",\n                                                        children: msg.content\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-xs mt-1 ${msg.role === \"user\" ? \"text-primary-200\" : \"text-gray-500\"}`,\n                                                        children: msg.timestamp.toLocaleTimeString([], {\n                                                            hour: \"2-digit\",\n                                                            minute: \"2-digit\"\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    msg.actions && msg.actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 ml-10 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-900\",\n                                                                children: action.type === \"create_task\" ? \"Create Task\" : action.type\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-700 mt-1\",\n                                                                children: JSON.stringify(action.data, null, 2)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    action.approved === undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleApproveAction(msg.id, index),\n                                                                className: \"p-1 rounded bg-green-100 hover:bg-green-200 text-green-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleRejectAction(msg.id, index),\n                                                                className: \"p-1 rounded bg-red-100 hover:bg-red-200 text-red-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    action.approved === true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-600 font-medium\",\n                                                        children: \"✓ Approved\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    action.approved === false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 font-medium\",\n                                                        children: \"✗ Rejected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, msg.id, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-lg px-3 py-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.1s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.2s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Ask me anything or describe what you'd like to work on...\",\n                                className: \"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                rows: 2,\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !message.trim() || isLoading,\n                                className: \"p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_PaperAirplaneIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-xs text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded\",\n                                children: \"Create a task\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-xs text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded\",\n                                children: \"Schedule meeting\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-xs text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded\",\n                                children: \"Project summary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\ChatPanel.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/ChatPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_services_setupService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/setupService */ \"(ssr)/./src/lib/services/setupService.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _ChatPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ChatPanel */ \"(ssr)/./src/components/layout/ChatPanel.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { sidebarOpen, chatPanelOpen } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useUIStore)();\n    const { setCurrentOrganization } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useOrganizationStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Load user's organization when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && user.role === \"admin\") {\n            const loadOrganization = async ()=>{\n                try {\n                    const defaultOrg = await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_6__.SetupService.getDefaultOrganization();\n                    if (defaultOrg) {\n                        setCurrentOrganization(defaultOrg);\n                    }\n                } catch (error) {\n                    console.error(\"Error loading organization:\", error);\n                }\n            };\n            loadOrganization();\n        } else {\n            setCurrentOrganization(null);\n        }\n    }, [\n        user,\n        setCurrentOrganization\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        user,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_5__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sidebarOpen ? \"w-64\" : \"w-0\"} transition-all duration-300 overflow-hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        onSignOut: handleSignOut\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex-1 overflow-auto ${chatPanelOpen ? \"mr-80\" : \"\"} transition-all duration-300`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"p-6\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${chatPanelOpen ? \"w-80\" : \"w-0\"} transition-all duration-300 overflow-hidden border-l border-gray-200`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChatBubbleLeftRightIcon,Cog6ToothIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header({ onSignOut }) {\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { toggleSidebar, toggleChatPanel, chatPanelOpen } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useUIStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleSidebar,\n                            className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"AI Productivity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleChatPanel,\n                            className: `p-2 rounded-lg transition-colors ${chatPanelOpen ? \"bg-primary-100 text-primary-600\" : \"hover:bg-gray-100 text-gray-600\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                            as: \"div\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Button, {\n                                    className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                    children: [\n                                        user?.photoURL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: user.photoURL,\n                                            alt: user.displayName,\n                                            className: \"h-8 w-8 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700 hidden sm:block\",\n                                            children: user?.displayName || user?.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Transition, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"transition ease-out duration-100\",\n                                    enterFrom: \"transform opacity-0 scale-95\",\n                                    enterTo: \"transform opacity-100 scale-100\",\n                                    leave: \"transition ease-in duration-75\",\n                                    leaveFrom: \"transform opacity-100 scale-100\",\n                                    leaveTo: \"transform opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Items, {\n                                        className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 focus:outline-none z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `${active ? \"bg-gray-50\" : \"\"} flex items-center w-full px-4 py-2 text-sm text-gray-700`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Profile\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `${active ? \"bg-gray-50\" : \"\"} flex items-center w-full px-4 py-2 text-sm text-gray-700`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Settings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-100 my-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: onSignOut,\n                                                            className: `${active ? \"bg-gray-50\" : \"\"} flex items-center w-full px-4 py-2 text-sm text-gray-700`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChatBubbleLeftRightIcon_Cog6ToothIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Sign out\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/RectangleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChevronDownIcon,ChevronRightIcon,Cog6ToothIcon,DocumentTextIcon,FolderIcon,HomeIcon,ListBulletIcon,PlusIcon,RectangleStackIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Projects\",\n        href: \"/projects\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Boards\",\n        href: \"/boards\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Tasks\",\n        href: \"/tasks\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Notes\",\n        href: \"/notes\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Calendar\",\n        href: \"/calendar\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Team\",\n        href: \"/team\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nconst adminNavigation = [\n    {\n        name: \"Admin Panel\",\n        href: \"/admin\",\n        icon: _barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { projects } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useProjectStore)();\n    const { currentOrganization } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useOrganizationStore)();\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const [projectsExpanded, setProjectsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isActive = (href)=>{\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-white border-r border-gray-200 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-sm font-semibold text-gray-900 truncate\",\n                                    children: currentOrganization?.name || \"Personal Workspace\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        projects.length,\n                                        \" projects\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-1 rounded hover:bg-gray-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4 space-y-1 overflow-y-auto\",\n                children: [\n                    navigation.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: item.href,\n                            className: `${isActive(item.href) ? \"bg-primary-50 text-primary-700 border-r-2 border-primary-700\" : \"text-gray-700 hover:bg-gray-50\"} group flex items-center px-3 py-2 text-sm font-medium rounded-l-lg transition-colors`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5 mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setProjectsExpanded(!projectsExpanded),\n                                className: \"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Projects\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    projectsExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            projectsExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-6 mt-1 space-y-1\",\n                                children: [\n                                    projects.slice(0, 5).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: `/projects/${project.id}`,\n                                            className: `${pathname.includes(project.id) ? \"text-primary-700 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"} block px-3 py-1 text-sm rounded-lg transition-colors truncate`,\n                                            children: project.name\n                                        }, project.id, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)),\n                                    projects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"px-3 py-1 text-xs text-gray-500\",\n                                        children: \"No projects yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/projects\",\n                                        className: \"block px-3 py-1 text-xs text-primary-600 hover:text-primary-700\",\n                                        children: \"View all projects →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    user?.role === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4 border-t border-gray-200\",\n                        children: adminNavigation.map((item)=>{\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                className: `${isActive(item.href) ? \"bg-primary-50 text-primary-700\" : \"text-gray-700 hover:bg-gray-50\"} group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-5 w-5 mr-3 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.name\n                                ]\n                            }, item.name, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChevronDownIcon_ChevronRightIcon_Cog6ToothIcon_DocumentTextIcon_FolderIcon_HomeIcon_ListBulletIcon_PlusIcon_RectangleStackIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        \"New Project\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\",\n    authDomain: \"zatconss.firebaseapp.com\",\n    projectId: \"zatconss\",\n    storageBucket: \"zatconss.firebasestorage.app\",\n    messagingSenderId: \"947257597349\",\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/services/setupService.ts":
/*!******************************************!*\
  !*** ./src/lib/services/setupService.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SetupService: () => (/* binding */ SetupService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\nclass SetupService {\n    /**\r\n   * Check if this is the first user (admin) in the system\r\n   */ static async isFirstUser() {\n        try {\n            const usersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(usersQuery);\n            return snapshot.empty;\n        } catch (error) {\n            console.error(\"Error checking if first user:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Create the admin user and default organization\r\n   */ static async createAdminUser(firebaseUser, displayName) {\n        try {\n            // Create admin user document - filter out undefined values\n            const adminUserData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"Admin\",\n                role: \"admin\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                adminUserData.photoURL = firebaseUser.photoURL;\n            }\n            const adminUser = adminUserData;\n            // Create default organization\n            const defaultOrganization = {\n                id: \"default-org\",\n                name: `${adminUser.displayName}'s Organization`,\n                description: \"Default organization for the admin user\",\n                ownerId: firebaseUser.uid,\n                members: {\n                    [firebaseUser.uid]: {\n                        role: \"admin\",\n                        joinedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n                    }\n                },\n                settings: {\n                    aiModels: {\n                        \"anthropic/claude-3-haiku\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Haiku (Fast)\",\n                            provider: \"openrouter\"\n                        },\n                        \"anthropic/claude-3-sonnet\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Sonnet (Balanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-4\": {\n                            enabled: true,\n                            displayName: \"GPT-4 (Advanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-3.5-turbo\": {\n                            enabled: true,\n                            displayName: \"GPT-3.5 Turbo (Fast)\",\n                            provider: \"openrouter\"\n                        }\n                    },\n                    mcpEnabled: false // Disabled by default for security\n                },\n                apiKeys: {\n                    openrouter: [] // Will be configured in admin panel\n                },\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n            };\n            // Save both documents\n            await Promise.all([\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), adminUser),\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"), defaultOrganization)\n            ]);\n            return {\n                user: adminUser,\n                organization: defaultOrganization\n            };\n        } catch (error) {\n            console.error(\"Error creating admin user:\", error);\n            // Provide more specific error messages\n            if (error.code === \"permission-denied\") {\n                throw new Error(\"Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.\");\n            } else if (error.code === \"unavailable\") {\n                throw new Error(\"Firestore is currently unavailable. Please try again later.\");\n            } else if (error.code === \"failed-precondition\") {\n                throw new Error(\"Firestore operation failed. Please check your Firebase configuration.\");\n            }\n            throw new Error(`Failed to create admin user: ${error.message}`);\n        }\n    }\n    /**\r\n   * Check if the system has been initialized\r\n   */ static async isSystemInitialized() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            return orgDoc.exists();\n        } catch (error) {\n            console.error(\"Error checking system initialization:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Get the default organization\r\n   */ static async getDefaultOrganization() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            if (orgDoc.exists()) {\n                return {\n                    id: orgDoc.id,\n                    ...orgDoc.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error getting default organization:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * Create a regular user (non-admin)\r\n   */ static async createRegularUser(firebaseUser, displayName) {\n        try {\n            // Create user document - filter out undefined values\n            const userData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"\",\n                role: \"user\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                userData.photoURL = firebaseUser.photoURL;\n            }\n            const user = userData;\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), userData);\n            return user;\n        } catch (error) {\n            console.error(\"Error creating regular user:\", error);\n            // Provide more specific error messages\n            if (error.code === \"permission-denied\") {\n                throw new Error(\"Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.\");\n            } else if (error.code === \"unavailable\") {\n                throw new Error(\"Firestore is currently unavailable. Please try again later.\");\n            } else if (error.code === \"failed-precondition\") {\n                throw new Error(\"Firestore operation failed. Please check your Firebase configuration.\");\n            }\n            throw new Error(`Failed to create user: ${error.message}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/services/setupService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useBoardStore: () => (/* binding */ useBoardStore),\n/* harmony export */   useChatStore: () => (/* binding */ useChatStore),\n/* harmony export */   useOrganizationStore: () => (/* binding */ useOrganizationStore),\n/* harmony export */   useProjectStore: () => (/* binding */ useProjectStore),\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        user: null,\n        loading: true,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            })\n    }));\nconst useOrganizationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentOrganization: null,\n        organizations: [],\n        setCurrentOrganization: (org)=>set({\n                currentOrganization: org\n            }),\n        setOrganizations: (orgs)=>set({\n                organizations: orgs\n            }),\n        addOrganization: (org)=>set({\n                organizations: [\n                    ...get().organizations,\n                    org\n                ]\n            }),\n        updateOrganization: (org)=>set({\n                organizations: get().organizations.map((o)=>o.id === org.id ? org : o),\n                currentOrganization: get().currentOrganization?.id === org.id ? org : get().currentOrganization\n            })\n    }));\nconst useProjectStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentProject: null,\n        projects: [],\n        setCurrentProject: (project)=>set({\n                currentProject: project\n            }),\n        setProjects: (projects)=>set({\n                projects\n            }),\n        addProject: (project)=>set({\n                projects: [\n                    ...get().projects,\n                    project\n                ]\n            }),\n        updateProject: (project)=>set({\n                projects: get().projects.map((p)=>p.id === project.id ? project : p),\n                currentProject: get().currentProject?.id === project.id ? project : get().currentProject\n            })\n    }));\nconst useBoardStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentBoard: null,\n        boards: [],\n        setCurrentBoard: (board)=>set({\n                currentBoard: board\n            }),\n        setBoards: (boards)=>set({\n                boards\n            }),\n        addBoard: (board)=>set({\n                boards: [\n                    ...get().boards,\n                    board\n                ]\n            }),\n        updateBoard: (board)=>set({\n                boards: get().boards.map((b)=>b.id === board.id ? board : b),\n                currentBoard: get().currentBoard?.id === board.id ? board : get().currentBoard\n            })\n    }));\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        selectedTask: null,\n        setTasks: (tasks)=>set({\n                tasks\n            }),\n        setSelectedTask: (task)=>set({\n                selectedTask: task\n            }),\n        addTask: (task)=>set({\n                tasks: [\n                    ...get().tasks,\n                    task\n                ]\n            }),\n        updateTask: (task)=>set({\n                tasks: get().tasks.map((t)=>t.id === task.id ? task : t),\n                selectedTask: get().selectedTask?.id === task.id ? task : get().selectedTask\n            }),\n        removeTask: (taskId)=>set({\n                tasks: get().tasks.filter((t)=>t.id !== taskId),\n                selectedTask: get().selectedTask?.id === taskId ? null : get().selectedTask\n            })\n    }));\nconst useChatStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentSession: null,\n        sessions: [],\n        isLoading: false,\n        setCurrentSession: (session)=>set({\n                currentSession: session\n            }),\n        setSessions: (sessions)=>set({\n                sessions\n            }),\n        addSession: (session)=>set({\n                sessions: [\n                    ...get().sessions,\n                    session\n                ]\n            }),\n        updateSession: (session)=>set({\n                sessions: get().sessions.map((s)=>s.id === session.id ? session : s),\n                currentSession: get().currentSession?.id === session.id ? session : get().currentSession\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        sidebarOpen: true,\n        chatPanelOpen: false,\n        taskModalOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        setChatPanelOpen: (open)=>set({\n                chatPanelOpen: open\n            }),\n        setTaskModalOpen: (open)=>set({\n                taskModalOpen: open\n            }),\n        toggleSidebar: ()=>set({\n                sidebarOpen: !get().sidebarOpen\n            }),\n        toggleChatPanel: ()=>set({\n                chatPanelOpen: !get().chatPanelOpen\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"49997f4810d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kNzM0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDk5OTdmNDgxMGQ0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthProvider */ \"(rsc)/./src/components/auth/AuthProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI Productivity Platform\",\n    description: \"Modern productivity platform with AI superpowers\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ21DO0FBSWxELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MscUVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcclxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgQXV0aFByb3ZpZGVyIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL0F1dGhQcm92aWRlcidcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdBSSBQcm9kdWN0aXZpdHkgUGxhdGZvcm0nLFxyXG4gIGRlc2NyaXB0aW9uOiAnTW9kZXJuIHByb2R1Y3Rpdml0eSBwbGF0Zm9ybSB3aXRoIEFJIHN1cGVycG93ZXJzJyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cclxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59Il0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\components\auth\AuthProvider.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/next","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/@headlessui"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();