/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/register/page";
exports.ids = ["app/auth/register/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/register/page.tsx */ \"(rsc)/./src/app/auth/register/page.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/register/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/register/page\",\n        pathname: \"/auth/register\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/AuthProvider.tsx */ \"(ssr)/./src/components/auth/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NvZGUtZmlsZXMlNUMlNUNhaSUyMGZpbGVzJTVDJTVDdG8tZG8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXV0aCU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcm9kdWN0aXZpdHktcGxhdGZvcm0vP2YwOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcY29kZS1maWxlc1xcXFxhaSBmaWxlc1xcXFx0by1kb1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXEF1dGhQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/register/page.tsx */ \"(ssr)/./src/app/auth/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUF3RyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByb2R1Y3Rpdml0eS1wbGF0Zm9ybS8/MDAwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNvZGUtZmlsZXNcXFxcYWkgZmlsZXNcXFxcdG8tZG9cXFxcc3JjXFxcXGFwcFxcXFxhdXRoXFxcXHJlZ2lzdGVyXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/register/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/register/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction RegisterPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleInputChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleEmailRegister = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            setLoading(false);\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseClient)();\n            const { error } = await supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password,\n                options: {\n                    data: {\n                        display_name: formData.displayName || formData.email.split(\"@\")[0]\n                    }\n                }\n            });\n            if (error) throw error;\n            setSuccess(\"Registration successful! Please check your email to confirm your account.\");\n            setFormData({\n                displayName: \"\",\n                email: \"\",\n                password: \"\",\n                confirmPassword: \"\"\n            });\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            let errorMessage = \"Registration failed\";\n            if (error.message) {\n                if (error.message.includes(\"User already registered\")) {\n                    errorMessage = \"An account already exists with this email address\";\n                } else if (error.message.includes(\"Invalid email\")) {\n                    errorMessage = \"Invalid email address\";\n                } else if (error.message.includes(\"Password should be at least\")) {\n                    errorMessage = \"Password must be at least 6 characters\";\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleRegister = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseClient)();\n            const { error } = await supabase.auth.signInWithOAuth({\n                provider: \"google\",\n                options: {\n                    redirectTo: `${window.location.origin}/dashboard`\n                }\n            });\n            if (error) throw error;\n        } catch (error) {\n            console.error(\"Google registration error:\", error);\n            setError(error.message || \"Failed to register with Google\");\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"FocusFlow AI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-2xl font-bold text-gray-900\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: [\n                                \"Already have an account?\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/auth/login\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleEmailRegister,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"displayName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Display Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"displayName\",\n                                                name: \"displayName\",\n                                                type: \"text\",\n                                                value: formData.displayName,\n                                                onChange: handleInputChange,\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                placeholder: \"Your display name\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: formData.email,\n                                                onChange: handleInputChange,\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                placeholder: \"Enter your email\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"confirmPassword\",\n                                                name: \"confirmPassword\",\n                                                type: \"password\",\n                                                autoComplete: \"new-password\",\n                                                required: true,\n                                                value: formData.confirmPassword,\n                                                onChange: handleInputChange,\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\",\n                                                placeholder: \"Confirm your password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? \"Creating account...\" : \"Create account\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white text-gray-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGoogleRegister,\n                                        disabled: loading,\n                                        className: \"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"currentColor\",\n                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"currentColor\",\n                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"currentColor\",\n                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"currentColor\",\n                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"Google\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/register/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthProvider({ children }) {\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseClient)();\n        // Get initial session\n        const getInitialSession = async ()=>{\n            setLoading(true);\n            try {\n                const { data: { session } } = await supabase.auth.getSession();\n                if (session?.user) {\n                    // Get user profile from our custom table\n                    const { data: profile, error } = await supabase.from(\"user_profiles\").select(\"*\").eq(\"id\", session.user.id).single();\n                    if (error && error.code !== \"PGRST116\") {\n                        console.error(\"Error fetching user profile:\", error);\n                        setUser(null);\n                    } else if (profile) {\n                        setUser(profile);\n                    } else {\n                        // Profile doesn't exist, it should be created by the trigger\n                        // But let's create it manually if needed\n                        const newProfile = {\n                            id: session.user.id,\n                            email: session.user.email || \"\",\n                            display_name: session.user.user_metadata?.display_name || session.user.email || \"\",\n                            photo_url: session.user.user_metadata?.avatar_url,\n                            role: \"user\",\n                            preferences: {\n                                theme: \"light\",\n                                notifications: true\n                            }\n                        };\n                        const { data: createdProfile, error: createError } = await supabase.from(\"user_profiles\").insert([\n                            newProfile\n                        ]).select().single();\n                        if (createError) {\n                            console.error(\"Error creating user profile:\", createError);\n                            setUser(null);\n                        } else {\n                            setUser(createdProfile);\n                        }\n                    }\n                } else {\n                    setUser(null);\n                }\n            } catch (error) {\n                console.error(\"Error in auth provider:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setLoading(true);\n            if (session?.user) {\n                // Get user profile\n                const { data: profile } = await supabase.from(\"user_profiles\").select(\"*\").eq(\"id\", session.user.id).single();\n                setUser(profile);\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useBoardStore: () => (/* binding */ useBoardStore),\n/* harmony export */   useChatStore: () => (/* binding */ useChatStore),\n/* harmony export */   useOrganizationStore: () => (/* binding */ useOrganizationStore),\n/* harmony export */   useProjectStore: () => (/* binding */ useProjectStore),\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        user: null,\n        loading: true,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            })\n    }));\nconst useOrganizationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentOrganization: null,\n        organizations: [],\n        setCurrentOrganization: (org)=>set({\n                currentOrganization: org\n            }),\n        setOrganizations: (orgs)=>set({\n                organizations: orgs\n            }),\n        addOrganization: (org)=>set({\n                organizations: [\n                    ...get().organizations,\n                    org\n                ]\n            }),\n        updateOrganization: (org)=>set({\n                organizations: get().organizations.map((o)=>o.id === org.id ? org : o),\n                currentOrganization: get().currentOrganization?.id === org.id ? org : get().currentOrganization\n            })\n    }));\nconst useProjectStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentProject: null,\n        projects: [],\n        setCurrentProject: (project)=>set({\n                currentProject: project\n            }),\n        setProjects: (projects)=>set({\n                projects\n            }),\n        addProject: (project)=>set({\n                projects: [\n                    ...get().projects,\n                    project\n                ]\n            }),\n        updateProject: (project)=>set({\n                projects: get().projects.map((p)=>p.id === project.id ? project : p),\n                currentProject: get().currentProject?.id === project.id ? project : get().currentProject\n            })\n    }));\nconst useBoardStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentBoard: null,\n        boards: [],\n        setCurrentBoard: (board)=>set({\n                currentBoard: board\n            }),\n        setBoards: (boards)=>set({\n                boards\n            }),\n        addBoard: (board)=>set({\n                boards: [\n                    ...get().boards,\n                    board\n                ]\n            }),\n        updateBoard: (board)=>set({\n                boards: get().boards.map((b)=>b.id === board.id ? board : b),\n                currentBoard: get().currentBoard?.id === board.id ? board : get().currentBoard\n            })\n    }));\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        selectedTask: null,\n        setTasks: (tasks)=>set({\n                tasks\n            }),\n        setSelectedTask: (task)=>set({\n                selectedTask: task\n            }),\n        addTask: (task)=>set({\n                tasks: [\n                    ...get().tasks,\n                    task\n                ]\n            }),\n        updateTask: (task)=>set({\n                tasks: get().tasks.map((t)=>t.id === task.id ? task : t),\n                selectedTask: get().selectedTask?.id === task.id ? task : get().selectedTask\n            }),\n        removeTask: (taskId)=>set({\n                tasks: get().tasks.filter((t)=>t.id !== taskId),\n                selectedTask: get().selectedTask?.id === taskId ? null : get().selectedTask\n            })\n    }));\nconst useChatStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentSession: null,\n        sessions: [],\n        isLoading: false,\n        setCurrentSession: (session)=>set({\n                currentSession: session\n            }),\n        setSessions: (sessions)=>set({\n                sessions\n            }),\n        addSession: (session)=>set({\n                sessions: [\n                    ...get().sessions,\n                    session\n                ]\n            }),\n        updateSession: (session)=>set({\n                sessions: get().sessions.map((s)=>s.id === session.id ? session : s),\n                currentSession: get().currentSession?.id === session.id ? session : get().currentSession\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        sidebarOpen: true,\n        chatPanelOpen: false,\n        taskModalOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        setChatPanelOpen: (open)=>set({\n                chatPanelOpen: open\n            }),\n        setTaskModalOpen: (open)=>set({\n                taskModalOpen: open\n            }),\n        toggleSidebar: ()=>set({\n                sidebarOpen: !get().sidebarOpen\n            }),\n        toggleChatPanel: ()=>set({\n                chatPanelOpen: !get().chatPanelOpen\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdminClient: () => (/* binding */ createSupabaseAdminClient),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://qwgffjjkcicexwqnhvqw.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3Z2ZmamprY2ljZXh3cW5odnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2ODgyNjAsImV4cCI6MjA2MDI2NDI2MH0.UCq6jp8EPCvp3cmuvR5bKVykebpP6AWIjsRB6AZPuZU\";\n// Client for use in client components\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client component helper\nconst createSupabaseClient = ()=>(0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Admin client for server-side operations\nconst createSupabaseAdminClient = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"49997f4810d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kNzM0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDk5OTdmNDgxMGQ0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/register/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/register/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\app\auth\register\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthProvider */ \"(rsc)/./src/components/auth/AuthProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI Productivity Platform\",\n    description: \"Modern productivity platform with AI superpowers\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ21DO0FBSWxELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MscUVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcclxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgQXV0aFByb3ZpZGVyIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL0F1dGhQcm92aWRlcidcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdBSSBQcm9kdWN0aXZpdHkgUGxhdGZvcm0nLFxyXG4gIGRlc2NyaXB0aW9uOiAnTW9kZXJuIHByb2R1Y3Rpdml0eSBwbGF0Zm9ybSB3aXRoIEFJIHN1cGVycG93ZXJzJyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cclxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59Il0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\components\auth\AuthProvider.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/@heroicons","vendor-chunks/use-sync-external-store","vendor-chunks/webidl-conversions","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fregister%2Fpage&page=%2Fauth%2Fregister%2Fpage&appPaths=%2Fauth%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fregister%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();