'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { 
  KeyIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  UsersIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { useAuthStore, useOrganizationStore } from '@/lib/store'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase'
import { AIService } from '@/lib/services/aiService'
import UserManagement from '@/components/admin/UserManagement'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

export default function AdminPage() {
  const { user } = useAuthStore()
  const { currentOrganization, updateOrganization } = useOrganizationStore()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(false)
  const [apiKeys, setApiKeys] = useState<string[]>([])
  const [newApiKey, setNewApiKey] = useState('')
  const [showApiKeys, setShowApiKeys] = useState<{ [key: number]: boolean }>({})
  const [aiModels, setAiModels] = useState<any>({})
  const [mcpEnabled, setMcpEnabled] = useState(false)

  // Redirect non-admin users
  useEffect(() => {
    if (user && user.role !== 'admin') {
      router.push('/dashboard')
    }
  }, [user, router])

  // Load organization settings
  useEffect(() => {
    if (currentOrganization) {
      setApiKeys(currentOrganization.apiKeys.openrouter || [])
      setAiModels(currentOrganization.settings.aiModels || {})
      setMcpEnabled(currentOrganization.settings.mcpEnabled || false)
    }
  }, [currentOrganization])

  const handleAddApiKey = async () => {
    if (!newApiKey.trim() || !currentOrganization) return

    try {
      setLoading(true)
      const updatedKeys = [...apiKeys, newApiKey.trim()]

      const supabase = createSupabaseClient()
      await supabase
        .from('organizations')
        .update({
          api_keys: { ...currentOrganization.api_keys, openrouter: updatedKeys },
          updated_at: new Date().toISOString()
        })
        .eq('id', currentOrganization.id)

      setApiKeys(updatedKeys)
      setNewApiKey('')
      
      // Update store
      updateOrganization({
        ...currentOrganization,
        apiKeys: { ...currentOrganization.apiKeys, openrouter: updatedKeys }
      })
    } catch (error) {
      console.error('Error adding API key:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveApiKey = async (index: number) => {
    if (!currentOrganization) return

    try {
      setLoading(true)
      const updatedKeys = apiKeys.filter((_, i) => i !== index)

      const supabase = createSupabaseClient()
      await supabase
        .from('organizations')
        .update({
          api_keys: { ...currentOrganization.api_keys, openrouter: updatedKeys },
          updated_at: new Date().toISOString()
        })
        .eq('id', currentOrganization.id)

      setApiKeys(updatedKeys)
      
      // Update store
      updateOrganization({
        ...currentOrganization,
        apiKeys: { ...currentOrganization.apiKeys, openrouter: updatedKeys }
      })
    } catch (error) {
      console.error('Error removing API key:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleModel = async (modelId: string) => {
    if (!currentOrganization) return

    try {
      setLoading(true)
      const updatedModels = {
        ...aiModels,
        [modelId]: {
          ...aiModels[modelId],
          enabled: !aiModels[modelId]?.enabled
        }
      }

      const supabase = createSupabaseClient()
      await supabase
        .from('organizations')
        .update({
          settings: { ...currentOrganization.settings, aiModels: updatedModels },
          updated_at: new Date().toISOString()
        })
        .eq('id', currentOrganization.id)

      setAiModels(updatedModels)
      
      // Update store
      updateOrganization({
        ...currentOrganization,
        settings: {
          ...currentOrganization.settings,
          aiModels: updatedModels
        }
      })
    } catch (error) {
      console.error('Error toggling model:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleMcp = async () => {
    if (!currentOrganization) return

    try {
      setLoading(true)
      const newMcpState = !mcpEnabled

      const supabase = createSupabaseClient()
      await supabase
        .from('organizations')
        .update({
          settings: { ...currentOrganization.settings, mcpEnabled: newMcpState },
          updated_at: new Date().toISOString()
        })
        .eq('id', currentOrganization.id)

      setMcpEnabled(newMcpState)
      
      // Update store
      updateOrganization({
        ...currentOrganization,
        settings: {
          ...currentOrganization.settings,
          mcpEnabled: newMcpState
        }
      })
    } catch (error) {
      console.error('Error toggling MCP:', error)
    } finally {
      setLoading(false)
    }
  }

  const availableModels = AIService.getAvailableModels()

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'api-keys', name: 'API Keys', icon: KeyIcon },
    { id: 'ai-models', name: 'AI Models', icon: CpuChipIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'users', name: 'Users', icon: UsersIcon },
  ]

  if (!user || user.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            You need admin privileges to access this page.
          </p>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <ProtectedRoute requireAdmin={true}>
      <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Panel</h1>
          <p className="text-gray-600 mt-1">Manage system settings, API keys, and user permissions</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">System Status</p>
                  <p className="text-lg font-bold text-green-600">Operational</p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <KeyIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">API Keys</p>
                  <p className="text-lg font-bold text-gray-900">{apiKeys.length}</p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CpuChipIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">AI Models</p>
                  <p className="text-lg font-bold text-gray-900">
                    {Object.values(aiModels).filter((model: any) => model.enabled).length}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">MCP Status</p>
                  <p className={`text-lg font-bold ${mcpEnabled ? 'text-green-600' : 'text-red-600'}`}>
                    {mcpEnabled ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'api-keys' && (
          <div className="space-y-6">
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">OpenRouter API Keys</h2>
              <p className="text-sm text-gray-600 mb-6">
                Add your OpenRouter API keys to enable AI features. Multiple keys provide redundancy and load balancing.
              </p>

              {/* Add New API Key */}
              <div className="flex space-x-3 mb-6">
                <input
                  type="password"
                  value={newApiKey}
                  onChange={(e) => setNewApiKey(e.target.value)}
                  placeholder="Enter OpenRouter API key"
                  className="input-field flex-1"
                />
                <button
                  onClick={handleAddApiKey}
                  disabled={loading || !newApiKey.trim()}
                  className="btn-primary flex items-center disabled:opacity-50"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Key
                </button>
              </div>

              {/* API Keys List */}
              <div className="space-y-3">
                {apiKeys.map((key, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <KeyIcon className="h-4 w-4 text-gray-400" />
                      <span className="font-mono text-sm">
                        {showApiKeys[index] ? key : `${'*'.repeat(key.length - 8)}${key.slice(-8)}`}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setShowApiKeys({ ...showApiKeys, [index]: !showApiKeys[index] })}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        {showApiKeys[index] ? (
                          <EyeSlashIcon className="h-4 w-4" />
                        ) : (
                          <EyeIcon className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => handleRemoveApiKey(index)}
                        className="p-1 text-red-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
                
                {apiKeys.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <KeyIcon className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                    <p>No API keys configured</p>
                    <p className="text-sm">Add an OpenRouter API key to enable AI features</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'ai-models' && (
          <div className="space-y-6">
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">AI Model Configuration</h2>
              <p className="text-sm text-gray-600 mb-6">
                Enable or disable specific AI models for your organization. Users will only see enabled models.
              </p>

              <div className="space-y-4">
                {availableModels.map((model) => (
                  <div key={model.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-900">{model.name}</h3>
                      <p className="text-sm text-gray-500">{model.provider} • {model.id}</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={aiModels[model.id]?.enabled || false}
                        onChange={() => handleToggleModel(model.id)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-6">
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h2>
              
              {/* MCP Toggle */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">MCP (Managed Command Panel)</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Allow AI to execute terminal commands with user approval. 
                      <span className="text-red-600 font-medium"> High security risk - use with caution.</span>
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={mcpEnabled}
                      onChange={handleToggleMcp}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                  </label>
                </div>
                
                {mcpEnabled && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-red-800">Security Warning</h4>
                        <p className="text-sm text-red-700 mt-1">
                          MCP allows AI to execute system commands. All commands require explicit user approval, 
                          but this feature should only be enabled in trusted environments.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <UserManagement />
        )}
      </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}