import { createSupabaseClient } from '@/lib/supabase'
import { Board, Task } from '@/lib/types'

export class BoardService {
  /**
   * Create a new board
   */
  static async createBoard(boardData: {
    name: string
    description?: string
    project_id: string
    organization_id: string
    columns?: any
  }): Promise<Board> {
    const supabase = createSupabaseClient()
    
    // Default columns if not provided
    const defaultColumns = boardData.columns || {
      'col-1': {
        id: 'col-1',
        title: 'To Do',
        position: 0,
        taskIds: []
      },
      'col-2': {
        id: 'col-2',
        title: 'In Progress',
        position: 1,
        taskIds: []
      },
      'col-3': {
        id: 'col-3',
        title: 'Done',
        position: 2,
        taskIds: []
      }
    }
    
    const { data, error } = await supabase
      .from('boards')
      .insert([{
        name: boardData.name,
        description: boardData.description || '',
        project_id: boardData.project_id,
        organization_id: boardData.organization_id,
        columns: defaultColumns
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating board:', error)
      throw new Error(error.message)
    }

    return data as Board
  }

  /**
   * Get all boards for a project
   */
  static async getProjectBoards(projectId: string): Promise<Board[]> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('boards')
      .select('*')
      .eq('project_id', projectId)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching project boards:', error)
      throw new Error(error.message)
    }

    return data as Board[]
  }

  /**
   * Get a single board by ID
   */
  static async getBoard(boardId: string): Promise<Board | null> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('boards')
      .select('*')
      .eq('id', boardId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Board not found
      }
      console.error('Error fetching board:', error)
      throw new Error(error.message)
    }

    return data as Board
  }

  /**
   * Update a board
   */
  static async updateBoard(boardId: string, updates: Partial<Board>): Promise<Board> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('boards')
      .update(updates)
      .eq('id', boardId)
      .select()
      .single()

    if (error) {
      console.error('Error updating board:', error)
      throw new Error(error.message)
    }

    return data as Board
  }

  /**
   * Delete a board
   */
  static async deleteBoard(boardId: string): Promise<void> {
    const supabase = createSupabaseClient()
    
    const { error } = await supabase
      .from('boards')
      .delete()
      .eq('id', boardId)

    if (error) {
      console.error('Error deleting board:', error)
      throw new Error(error.message)
    }
  }

  /**
   * Get all tasks for a board
   */
  static async getBoardTasks(boardId: string): Promise<Task[]> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:user_profiles(id, display_name, email, photo_url)
      `)
      .eq('board_id', boardId)
      .order('position', { ascending: true })

    if (error) {
      console.error('Error fetching board tasks:', error)
      throw new Error(error.message)
    }

    return data as Task[]
  }

  /**
   * Update board columns
   */
  static async updateBoardColumns(boardId: string, columns: any): Promise<Board> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('boards')
      .update({ columns })
      .eq('id', boardId)
      .select()
      .single()

    if (error) {
      console.error('Error updating board columns:', error)
      throw new Error(error.message)
    }

    return data as Board
  }
}
