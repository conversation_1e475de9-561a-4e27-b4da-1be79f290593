'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { useAuthStore, useBoardStore, useTaskStore } from '@/lib/store'
import DashboardLayout from '@/components/layout/DashboardLayout'
import {
  PlusIcon,
  EllipsisHorizontalIcon,
  UserIcon,
  CalendarIcon,
  FlagIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { Board, Task } from '@/lib/types'

const priorityColors = {
  low: 'border-l-green-500',
  medium: 'border-l-yellow-500',
  high: 'border-l-red-500'
}

export default function BoardDetailPage() {
  const params = useParams()
  const boardId = params.id as string
  const { user } = useAuthStore()
  const { boards } = useBoardStore()
  const { tasks, setTasks } = useTaskStore()
  const [board, setBoard] = useState<Board | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // TODO: Load board and tasks from Firestore
    const loadBoardData = async () => {
      try {
        // Mock board data
        const mockBoard: Board = {
          id: boardId,
          name: 'Product Development',
          description: 'Main product development board',
          projectId: 'project-1',
          organizationId: 'default-org',
          columns: {
            'col-1': {
              id: 'col-1',
              title: 'To Do',
              position: 0,
              taskIds: ['task-1', 'task-2']
            },
            'col-2': {
              id: 'col-2',
              title: 'In Progress',
              position: 1,
              taskIds: ['task-3']
            },
            'col-3': {
              id: 'col-3',
              title: 'Done',
              position: 2,
              taskIds: ['task-4']
            }
          },
          createdAt: new Date() as any,
          updatedAt: new Date() as any
        }

        // Mock tasks data
        const mockTasks: Task[] = [
          {
            id: 'task-1',
            title: 'Design new landing page',
            description: 'Create a modern, responsive landing page',
            status: 'todo',
            priority: 'high',
            assigneeId: user?.id || '',
            projectId: 'project-1',
            boardId: boardId,
            columnId: 'col-1',
            position: 0,
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) as any,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
            subtasks: [],
            tags: ['design', 'frontend'],
            timeTracking: { estimated: 8, logged: 0 }
          },
          {
            id: 'task-2',
            title: 'Fix authentication bug',
            description: 'Users unable to log in with Google OAuth',
            status: 'todo',
            priority: 'high',
            assigneeId: user?.id || '',
            projectId: 'project-1',
            boardId: boardId,
            columnId: 'col-1',
            position: 1,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
            subtasks: [],
            tags: ['bug', 'auth'],
            timeTracking: { estimated: 4, logged: 0 }
          },
          {
            id: 'task-3',
            title: 'Implement user dashboard',
            description: 'Create user dashboard with analytics',
            status: 'in-progress',
            priority: 'medium',
            assigneeId: user?.id || '',
            projectId: 'project-1',
            boardId: boardId,
            columnId: 'col-2',
            position: 0,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
            subtasks: [],
            tags: ['dashboard', 'analytics'],
            timeTracking: { estimated: 12, logged: 6 }
          },
          {
            id: 'task-4',
            title: 'Setup CI/CD pipeline',
            description: 'Configure automated deployment pipeline',
            status: 'done',
            priority: 'low',
            assigneeId: user?.id || '',
            projectId: 'project-1',
            boardId: boardId,
            columnId: 'col-3',
            position: 0,
            createdAt: new Date() as any,
            updatedAt: new Date() as any,
            subtasks: [],
            tags: ['devops', 'automation'],
            timeTracking: { estimated: 6, logged: 6 }
          }
        ]

        setBoard(mockBoard)
        setTasks(mockTasks)
      } catch (error) {
        console.error('Error loading board data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadBoardData()
  }, [boardId, setTasks, user])

  const getTasksForColumn = (columnId: string) => {
    return tasks.filter(task => task.columnId === columnId)
      .sort((a, b) => a.position - b.position)
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (!board) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Board not found</h3>
          <p className="text-gray-500">The board you're looking for doesn't exist.</p>
        </div>
      </DashboardLayout>
    )
  }

  const columns = Object.values(board.columns).sort((a, b) => a.position - b.position)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{board.name}</h1>
            {board.description && (
              <p className="text-gray-600">{board.description}</p>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="btn-secondary flex items-center space-x-2">
              <UserIcon className="h-4 w-4" />
              <span>Share</span>
            </button>
            <button className="btn-primary flex items-center space-x-2">
              <PlusIcon className="h-4 w-4" />
              <span>Add Task</span>
            </button>
          </div>
        </div>

        {/* Kanban Board */}
        <div className="flex space-x-6 overflow-x-auto pb-6 min-h-[600px]">
          {columns.map((column) => {
            const columnTasks = getTasksForColumn(column.id)

            return (
              <div key={column.id} className="flex-shrink-0 w-80">
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm h-full">
                  {/* Column Header */}
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          column.title === 'To Do' ? 'bg-gray-400' :
                          column.title === 'In Progress' ? 'bg-blue-500' :
                          'bg-green-500'
                        }`}></div>
                        <h3 className="font-semibold text-gray-900 text-sm uppercase tracking-wide">{column.title}</h3>
                        <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium">
                          {columnTasks.length}
                        </span>
                      </div>
                      <button className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors">
                        <EllipsisHorizontalIcon className="h-4 w-4 text-gray-400" />
                      </button>
                    </div>
                  </div>

                  {/* Tasks */}
                  <div className="p-4 space-y-3 max-h-[500px] overflow-y-auto">
                    {columnTasks.map((task) => (
                      <div
                        key={task.id}
                        className="bg-white rounded-lg p-4 border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium text-gray-900 text-sm leading-tight flex-1 pr-2">{task.title}</h4>
                          <div className={`w-2 h-2 rounded-full flex-shrink-0 mt-1 ${
                            task.priority === 'high' ? 'bg-red-500' :
                            task.priority === 'medium' ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}></div>
                        </div>

                        {task.description && (
                          <p className="text-xs text-gray-600 mb-3 line-clamp-2 leading-relaxed">
                            {task.description}
                          </p>
                        )}

                        {/* Tags */}
                        {task.tags && task.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {task.tags.map((tag) => (
                              <span
                                key={tag}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-50 text-blue-700 font-medium"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Task Meta */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {task.dueDate && (
                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <CalendarIcon className="h-3 w-3" />
                                <span>{new Date(task.dueDate.seconds * 1000).toLocaleDateString()}</span>
                              </div>
                            )}

                            {task.timeTracking && (
                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <ClockIcon className="h-3 w-3" />
                                <span>{task.timeTracking.logged}h / {task.timeTracking.estimated}h</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                              <UserIcon className="h-3 w-3 text-gray-600" />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Add Task Button */}
                    <div className="p-4 border-t border-gray-100">
                      <button className="w-full p-3 border border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-300 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 group">
                        <PlusIcon className="h-4 w-4 mx-auto mb-1 group-hover:text-blue-600" />
                        <span className="text-sm font-medium">Add task</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}

          {/* Add Column */}
          <div className="flex-shrink-0 w-80">
            <div className="bg-white rounded-xl border-2 border-dashed border-gray-300 h-full min-h-[200px] flex flex-col items-center justify-center hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 cursor-pointer group">
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3 group-hover:bg-blue-100 transition-colors">
                  <PlusIcon className="h-6 w-6 text-gray-400 group-hover:text-blue-600" />
                </div>
                <h3 className="text-sm font-medium text-gray-600 group-hover:text-blue-600">Add Column</h3>
                <p className="text-xs text-gray-500 mt-1">Create a new status column</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
