'use client'

import { useEffect } from 'react'
import { useAuthStore, useUIStore, useOrganizationStore } from '@/lib/store'
import { auth } from '@/lib/firebase'
import { useRouter } from 'next/navigation'
import { signOut } from 'firebase/auth'
import { SetupService } from '@/lib/services/setupService'
import Sidebar from './Sidebar'
import ChatPanel from './ChatPanel'
import Header from './Header'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user } = useAuthStore()
  const { sidebarOpen, chatPanelOpen } = useUIStore()
  const { setCurrentOrganization } = useOrganizationStore()
  const router = useRouter()

  // Load user's organization when user changes
  useEffect(() => {
    if (user && user.role === 'admin') {
      const loadOrganization = async () => {
        try {
          const defaultOrg = await SetupService.getDefaultOrganization()
          if (defaultOrg) {
            setCurrentOrganization(defaultOrg)
          }
        } catch (error) {
          console.error('Error loading organization:', error)
        }
      }
      loadOrganization()
    } else {
      setCurrentOrganization(null)
    }
  }, [user, setCurrentOrganization])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth/login')
    }
  }, [user, router])

  const handleSignOut = async () => {
    try {
      await signOut(auth)
      router.push('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-0'} transition-all duration-300 overflow-hidden`}>
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header onSignOut={handleSignOut} />
        
        <div className="flex-1 flex overflow-hidden">
          {/* Main Content Area */}
          <div className={`flex-1 overflow-auto ${chatPanelOpen ? 'mr-80' : ''} transition-all duration-300`}>
            <main className="p-6">
              {children}
            </main>
          </div>

          {/* Chat Panel */}
          <div className={`${chatPanelOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden border-l border-gray-200`}>
            <ChatPanel />
          </div>
        </div>
      </div>
    </div>
  )
}