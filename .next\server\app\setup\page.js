/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/setup/page";
exports.ids = ["app/setup/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'setup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/setup/page.tsx */ \"(rsc)/./src/app/setup/page.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/setup/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/setup/page\",\n        pathname: \"/setup\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/AuthProvider.tsx */ \"(ssr)/./src/components/auth/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NvZGUtZmlsZXMlNUMlNUNhaSUyMGZpbGVzJTVDJTVDdG8tZG8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXV0aCU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcm9kdWN0aXZpdHktcGxhdGZvcm0vP2YwOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcY29kZS1maWxlc1xcXFxhaSBmaWxlc1xcXFx0by1kb1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXEF1dGhQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/setup/page.tsx */ \"(ssr)/./src/app/setup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc2V0dXAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQStGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLz84ZTdjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcY29kZS1maWxlc1xcXFxhaSBmaWxlc1xcXFx0by1kb1xcXFxzcmNcXFxcYXBwXFxcXHNldHVwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/setup/page.tsx":
/*!********************************!*\
  !*** ./src/app/setup/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SetupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/setupService */ \"(ssr)/./src/lib/services/setupService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SetupPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        organizationName: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleInputChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleEmailSetup = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            setLoading(false);\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters\");\n            setLoading(false);\n            return;\n        }\n        try {\n            // Create Firebase user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, formData.email, formData.password);\n            // Update display name\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n                displayName: formData.displayName\n            });\n            // Create admin user and organization\n            await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__.SetupService.createAdminUser(userCredential.user, formData.displayName);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleSetup = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const provider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();\n            const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, provider);\n            // Create admin user and organization\n            await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__.SetupService.createAdminUser(result.user, result.user.displayName || \"\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                            children: \"Welcome to AI Productivity Platform\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"You're the first user! Let's set up your admin account and get started.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 shadow-sm border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"What you'll get as admin:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Full administrative control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"AI model configuration & API key management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Team member management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"Advanced features like MCP terminal access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6 bg-white rounded-lg p-6 shadow-sm border border-gray-200\",\n                    onSubmit: handleEmailSetup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Create Admin Account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"displayName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"displayName\",\n                                            name: \"displayName\",\n                                            type: \"text\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Enter your full name\",\n                                            value: formData.displayName,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Enter your email\",\n                                            value: formData.email,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Create a strong password\",\n                                                    value: formData.password,\n                                                    onChange: handleInputChange\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Confirm your password\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"Setting up...\" : \"Create Admin Account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white text-gray-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleGoogleSetup,\n                                    disabled: loading,\n                                    className: \"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center\",\n                            children: [\n                                \"By creating an account, you agree to our Terms of Service and Privacy Policy.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                \"As the first user, you'll have full administrative privileges.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\setup\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/setup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AuthProvider({ children }) {\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthProvider: Setting up auth state listener\");\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (firebaseUser)=>{\n            console.log(\"AuthProvider: Auth state changed:\", firebaseUser ? firebaseUser.uid : \"null\");\n            setLoading(true);\n            if (firebaseUser) {\n                try {\n                    // Get user document from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        console.log(\"AuthProvider: User document found, setting user:\", userData.id);\n                        setUser(userData);\n                    } else {\n                        // User exists in Firebase Auth but not in Firestore\n                        // Create the user document automatically\n                        console.log(\"Creating missing user document for:\", firebaseUser.uid);\n                        const newUser = {\n                            id: firebaseUser.uid,\n                            email: firebaseUser.email || \"\",\n                            displayName: firebaseUser.displayName || \"\",\n                            photoURL: firebaseUser.photoURL || undefined,\n                            role: \"user\",\n                            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            preferences: {\n                                theme: \"light\",\n                                notifications: true\n                            }\n                        };\n                        try {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid), newUser);\n                            setUser(newUser);\n                            console.log(\"User document created successfully\");\n                        } catch (error) {\n                            console.error(\"Error creating user document:\", error);\n                            setUser(null);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user data:\", error);\n                    setUser(null);\n                }\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\",\n    authDomain: \"zatconss.firebaseapp.com\",\n    projectId: \"zatconss\",\n    storageBucket: \"zatconss.firebasestorage.app\",\n    messagingSenderId: \"947257597349\",\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/services/setupService.ts":
/*!******************************************!*\
  !*** ./src/lib/services/setupService.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SetupService: () => (/* binding */ SetupService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\nclass SetupService {\n    /**\r\n   * Check if this is the first user (admin) in the system\r\n   */ static async isFirstUser() {\n        try {\n            const usersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(usersQuery);\n            return snapshot.empty;\n        } catch (error) {\n            console.error(\"Error checking if first user:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Create the admin user and default organization\r\n   */ static async createAdminUser(firebaseUser, displayName) {\n        try {\n            // Create admin user document - filter out undefined values\n            const adminUserData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"Admin\",\n                role: \"admin\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                adminUserData.photoURL = firebaseUser.photoURL;\n            }\n            const adminUser = adminUserData;\n            // Create default organization\n            const defaultOrganization = {\n                id: \"default-org\",\n                name: `${adminUser.displayName}'s Organization`,\n                description: \"Default organization for the admin user\",\n                ownerId: firebaseUser.uid,\n                members: {\n                    [firebaseUser.uid]: {\n                        role: \"admin\",\n                        joinedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n                    }\n                },\n                settings: {\n                    aiModels: {\n                        \"anthropic/claude-3-haiku\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Haiku (Fast)\",\n                            provider: \"openrouter\"\n                        },\n                        \"anthropic/claude-3-sonnet\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Sonnet (Balanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-4\": {\n                            enabled: true,\n                            displayName: \"GPT-4 (Advanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-3.5-turbo\": {\n                            enabled: true,\n                            displayName: \"GPT-3.5 Turbo (Fast)\",\n                            provider: \"openrouter\"\n                        }\n                    },\n                    mcpEnabled: false // Disabled by default for security\n                },\n                apiKeys: {\n                    openrouter: [] // Will be configured in admin panel\n                },\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n            };\n            // Save both documents\n            await Promise.all([\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), adminUser),\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"), defaultOrganization)\n            ]);\n            return {\n                user: adminUser,\n                organization: defaultOrganization\n            };\n        } catch (error) {\n            console.error(\"Error creating admin user:\", error);\n            // Provide more specific error messages\n            if (error.code === \"permission-denied\") {\n                throw new Error(\"Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.\");\n            } else if (error.code === \"unavailable\") {\n                throw new Error(\"Firestore is currently unavailable. Please try again later.\");\n            } else if (error.code === \"failed-precondition\") {\n                throw new Error(\"Firestore operation failed. Please check your Firebase configuration.\");\n            }\n            throw new Error(`Failed to create admin user: ${error.message}`);\n        }\n    }\n    /**\r\n   * Check if the system has been initialized\r\n   */ static async isSystemInitialized() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            return orgDoc.exists();\n        } catch (error) {\n            console.error(\"Error checking system initialization:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Get the default organization\r\n   */ static async getDefaultOrganization() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            if (orgDoc.exists()) {\n                return {\n                    id: orgDoc.id,\n                    ...orgDoc.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error getting default organization:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * Create a regular user (non-admin)\r\n   */ static async createRegularUser(firebaseUser, displayName) {\n        try {\n            // Create user document - filter out undefined values\n            const userData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"\",\n                role: \"user\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                userData.photoURL = firebaseUser.photoURL;\n            }\n            const user = userData;\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), userData);\n            return user;\n        } catch (error) {\n            console.error(\"Error creating regular user:\", error);\n            // Provide more specific error messages\n            if (error.code === \"permission-denied\") {\n                throw new Error(\"Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.\");\n            } else if (error.code === \"unavailable\") {\n                throw new Error(\"Firestore is currently unavailable. Please try again later.\");\n            } else if (error.code === \"failed-precondition\") {\n                throw new Error(\"Firestore operation failed. Please check your Firebase configuration.\");\n            }\n            throw new Error(`Failed to create user: ${error.message}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/services/setupService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useBoardStore: () => (/* binding */ useBoardStore),\n/* harmony export */   useChatStore: () => (/* binding */ useChatStore),\n/* harmony export */   useOrganizationStore: () => (/* binding */ useOrganizationStore),\n/* harmony export */   useProjectStore: () => (/* binding */ useProjectStore),\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        user: null,\n        loading: true,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            })\n    }));\nconst useOrganizationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentOrganization: null,\n        organizations: [],\n        setCurrentOrganization: (org)=>set({\n                currentOrganization: org\n            }),\n        setOrganizations: (orgs)=>set({\n                organizations: orgs\n            }),\n        addOrganization: (org)=>set({\n                organizations: [\n                    ...get().organizations,\n                    org\n                ]\n            }),\n        updateOrganization: (org)=>set({\n                organizations: get().organizations.map((o)=>o.id === org.id ? org : o),\n                currentOrganization: get().currentOrganization?.id === org.id ? org : get().currentOrganization\n            })\n    }));\nconst useProjectStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentProject: null,\n        projects: [],\n        setCurrentProject: (project)=>set({\n                currentProject: project\n            }),\n        setProjects: (projects)=>set({\n                projects\n            }),\n        addProject: (project)=>set({\n                projects: [\n                    ...get().projects,\n                    project\n                ]\n            }),\n        updateProject: (project)=>set({\n                projects: get().projects.map((p)=>p.id === project.id ? project : p),\n                currentProject: get().currentProject?.id === project.id ? project : get().currentProject\n            })\n    }));\nconst useBoardStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentBoard: null,\n        boards: [],\n        setCurrentBoard: (board)=>set({\n                currentBoard: board\n            }),\n        setBoards: (boards)=>set({\n                boards\n            }),\n        addBoard: (board)=>set({\n                boards: [\n                    ...get().boards,\n                    board\n                ]\n            }),\n        updateBoard: (board)=>set({\n                boards: get().boards.map((b)=>b.id === board.id ? board : b),\n                currentBoard: get().currentBoard?.id === board.id ? board : get().currentBoard\n            })\n    }));\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        selectedTask: null,\n        setTasks: (tasks)=>set({\n                tasks\n            }),\n        setSelectedTask: (task)=>set({\n                selectedTask: task\n            }),\n        addTask: (task)=>set({\n                tasks: [\n                    ...get().tasks,\n                    task\n                ]\n            }),\n        updateTask: (task)=>set({\n                tasks: get().tasks.map((t)=>t.id === task.id ? task : t),\n                selectedTask: get().selectedTask?.id === task.id ? task : get().selectedTask\n            }),\n        removeTask: (taskId)=>set({\n                tasks: get().tasks.filter((t)=>t.id !== taskId),\n                selectedTask: get().selectedTask?.id === taskId ? null : get().selectedTask\n            })\n    }));\nconst useChatStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentSession: null,\n        sessions: [],\n        isLoading: false,\n        setCurrentSession: (session)=>set({\n                currentSession: session\n            }),\n        setSessions: (sessions)=>set({\n                sessions\n            }),\n        addSession: (session)=>set({\n                sessions: [\n                    ...get().sessions,\n                    session\n                ]\n            }),\n        updateSession: (session)=>set({\n                sessions: get().sessions.map((s)=>s.id === session.id ? session : s),\n                currentSession: get().currentSession?.id === session.id ? session : get().currentSession\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        sidebarOpen: true,\n        chatPanelOpen: false,\n        taskModalOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        setChatPanelOpen: (open)=>set({\n                chatPanelOpen: open\n            }),\n        setTaskModalOpen: (open)=>set({\n                taskModalOpen: open\n            }),\n        toggleSidebar: ()=>set({\n                sidebarOpen: !get().sidebarOpen\n            }),\n        toggleChatPanel: ()=>set({\n                chatPanelOpen: !get().chatPanelOpen\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWdDO0FBV3pCLE1BQU1DLGVBQWVELCtDQUFNQSxDQUFZLENBQUNFLE1BQVM7UUFDdERDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxTQUFTLENBQUNGLE9BQVNELElBQUk7Z0JBQUVDO1lBQUs7UUFDOUJHLFlBQVksQ0FBQ0YsVUFBWUYsSUFBSTtnQkFBRUU7WUFBUTtJQUN6QyxJQUFHO0FBWUksTUFBTUcsdUJBQXVCUCwrQ0FBTUEsQ0FBb0IsQ0FBQ0UsS0FBS00sTUFBUztRQUMzRUMscUJBQXFCO1FBQ3JCQyxlQUFlLEVBQUU7UUFDakJDLHdCQUF3QixDQUFDQyxNQUFRVixJQUFJO2dCQUFFTyxxQkFBcUJHO1lBQUk7UUFDaEVDLGtCQUFrQixDQUFDQyxPQUFTWixJQUFJO2dCQUFFUSxlQUFlSTtZQUFLO1FBQ3REQyxpQkFBaUIsQ0FBQ0gsTUFBUVYsSUFBSTtnQkFBRVEsZUFBZTt1QkFBSUYsTUFBTUUsYUFBYTtvQkFBRUU7aUJBQUk7WUFBQztRQUM3RUksb0JBQW9CLENBQUNKLE1BQVFWLElBQUk7Z0JBQy9CUSxlQUFlRixNQUFNRSxhQUFhLENBQUNPLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLUCxJQUFJTyxFQUFFLEdBQUdQLE1BQU1NO2dCQUNwRVQscUJBQXFCRCxNQUFNQyxtQkFBbUIsRUFBRVUsT0FBT1AsSUFBSU8sRUFBRSxHQUFHUCxNQUFNSixNQUFNQyxtQkFBbUI7WUFDakc7SUFDRixJQUFHO0FBWUksTUFBTVcsa0JBQWtCcEIsK0NBQU1BLENBQWUsQ0FBQ0UsS0FBS00sTUFBUztRQUNqRWEsZ0JBQWdCO1FBQ2hCQyxVQUFVLEVBQUU7UUFDWkMsbUJBQW1CLENBQUNDLFVBQVl0QixJQUFJO2dCQUFFbUIsZ0JBQWdCRztZQUFRO1FBQzlEQyxhQUFhLENBQUNILFdBQWFwQixJQUFJO2dCQUFFb0I7WUFBUztRQUMxQ0ksWUFBWSxDQUFDRixVQUFZdEIsSUFBSTtnQkFBRW9CLFVBQVU7dUJBQUlkLE1BQU1jLFFBQVE7b0JBQUVFO2lCQUFRO1lBQUM7UUFDdEVHLGVBQWUsQ0FBQ0gsVUFBWXRCLElBQUk7Z0JBQzlCb0IsVUFBVWQsTUFBTWMsUUFBUSxDQUFDTCxHQUFHLENBQUNXLENBQUFBLElBQUtBLEVBQUVULEVBQUUsS0FBS0ssUUFBUUwsRUFBRSxHQUFHSyxVQUFVSTtnQkFDbEVQLGdCQUFnQmIsTUFBTWEsY0FBYyxFQUFFRixPQUFPSyxRQUFRTCxFQUFFLEdBQUdLLFVBQVVoQixNQUFNYSxjQUFjO1lBQzFGO0lBQ0YsSUFBRztBQVlJLE1BQU1RLGdCQUFnQjdCLCtDQUFNQSxDQUFhLENBQUNFLEtBQUtNLE1BQVM7UUFDN0RzQixjQUFjO1FBQ2RDLFFBQVEsRUFBRTtRQUNWQyxpQkFBaUIsQ0FBQ0MsUUFBVS9CLElBQUk7Z0JBQUU0QixjQUFjRztZQUFNO1FBQ3REQyxXQUFXLENBQUNILFNBQVc3QixJQUFJO2dCQUFFNkI7WUFBTztRQUNwQ0ksVUFBVSxDQUFDRixRQUFVL0IsSUFBSTtnQkFBRTZCLFFBQVE7dUJBQUl2QixNQUFNdUIsTUFBTTtvQkFBRUU7aUJBQU07WUFBQztRQUM1REcsYUFBYSxDQUFDSCxRQUFVL0IsSUFBSTtnQkFDMUI2QixRQUFRdkIsTUFBTXVCLE1BQU0sQ0FBQ2QsR0FBRyxDQUFDb0IsQ0FBQUEsSUFBS0EsRUFBRWxCLEVBQUUsS0FBS2MsTUFBTWQsRUFBRSxHQUFHYyxRQUFRSTtnQkFDMURQLGNBQWN0QixNQUFNc0IsWUFBWSxFQUFFWCxPQUFPYyxNQUFNZCxFQUFFLEdBQUdjLFFBQVF6QixNQUFNc0IsWUFBWTtZQUNoRjtJQUNGLElBQUc7QUFhSSxNQUFNUSxlQUFldEMsK0NBQU1BLENBQVksQ0FBQ0UsS0FBS00sTUFBUztRQUMzRCtCLE9BQU8sRUFBRTtRQUNUQyxjQUFjO1FBQ2RDLFVBQVUsQ0FBQ0YsUUFBVXJDLElBQUk7Z0JBQUVxQztZQUFNO1FBQ2pDRyxpQkFBaUIsQ0FBQ0MsT0FBU3pDLElBQUk7Z0JBQUVzQyxjQUFjRztZQUFLO1FBQ3BEQyxTQUFTLENBQUNELE9BQVN6QyxJQUFJO2dCQUFFcUMsT0FBTzt1QkFBSS9CLE1BQU0rQixLQUFLO29CQUFFSTtpQkFBSztZQUFDO1FBQ3ZERSxZQUFZLENBQUNGLE9BQVN6QyxJQUFJO2dCQUN4QnFDLE9BQU8vQixNQUFNK0IsS0FBSyxDQUFDdEIsR0FBRyxDQUFDNkIsQ0FBQUEsSUFBS0EsRUFBRTNCLEVBQUUsS0FBS3dCLEtBQUt4QixFQUFFLEdBQUd3QixPQUFPRztnQkFDdEROLGNBQWNoQyxNQUFNZ0MsWUFBWSxFQUFFckIsT0FBT3dCLEtBQUt4QixFQUFFLEdBQUd3QixPQUFPbkMsTUFBTWdDLFlBQVk7WUFDOUU7UUFDQU8sWUFBWSxDQUFDQyxTQUFXOUMsSUFBSTtnQkFDMUJxQyxPQUFPL0IsTUFBTStCLEtBQUssQ0FBQ1UsTUFBTSxDQUFDSCxDQUFBQSxJQUFLQSxFQUFFM0IsRUFBRSxLQUFLNkI7Z0JBQ3hDUixjQUFjaEMsTUFBTWdDLFlBQVksRUFBRXJCLE9BQU82QixTQUFTLE9BQU94QyxNQUFNZ0MsWUFBWTtZQUM3RTtJQUNGLElBQUc7QUFjSSxNQUFNVSxlQUFlbEQsK0NBQU1BLENBQVksQ0FBQ0UsS0FBS00sTUFBUztRQUMzRDJDLGdCQUFnQjtRQUNoQkMsVUFBVSxFQUFFO1FBQ1pDLFdBQVc7UUFDWEMsbUJBQW1CLENBQUNDLFVBQVlyRCxJQUFJO2dCQUFFaUQsZ0JBQWdCSTtZQUFRO1FBQzlEQyxhQUFhLENBQUNKLFdBQWFsRCxJQUFJO2dCQUFFa0Q7WUFBUztRQUMxQ0ssWUFBWSxDQUFDRixVQUFZckQsSUFBSTtnQkFBRWtELFVBQVU7dUJBQUk1QyxNQUFNNEMsUUFBUTtvQkFBRUc7aUJBQVE7WUFBQztRQUN0RUcsZUFBZSxDQUFDSCxVQUFZckQsSUFBSTtnQkFDOUJrRCxVQUFVNUMsTUFBTTRDLFFBQVEsQ0FBQ25DLEdBQUcsQ0FBQzBDLENBQUFBLElBQUtBLEVBQUV4QyxFQUFFLEtBQUtvQyxRQUFRcEMsRUFBRSxHQUFHb0MsVUFBVUk7Z0JBQ2xFUixnQkFBZ0IzQyxNQUFNMkMsY0FBYyxFQUFFaEMsT0FBT29DLFFBQVFwQyxFQUFFLEdBQUdvQyxVQUFVL0MsTUFBTTJDLGNBQWM7WUFDMUY7UUFDQTdDLFlBQVksQ0FBQ0YsVUFBWUYsSUFBSTtnQkFBRW1ELFdBQVdqRDtZQUFRO0lBQ3BELElBQUc7QUFjSSxNQUFNd0QsYUFBYTVELCtDQUFNQSxDQUFVLENBQUNFLEtBQUtNLE1BQVM7UUFDdkRxRCxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsZUFBZTtRQUNmQyxnQkFBZ0IsQ0FBQ0MsT0FBUy9ELElBQUk7Z0JBQUUyRCxhQUFhSTtZQUFLO1FBQ2xEQyxrQkFBa0IsQ0FBQ0QsT0FBUy9ELElBQUk7Z0JBQUU0RCxlQUFlRztZQUFLO1FBQ3RERSxrQkFBa0IsQ0FBQ0YsT0FBUy9ELElBQUk7Z0JBQUU2RCxlQUFlRTtZQUFLO1FBQ3RERyxlQUFlLElBQU1sRSxJQUFJO2dCQUFFMkQsYUFBYSxDQUFDckQsTUFBTXFELFdBQVc7WUFBQztRQUMzRFEsaUJBQWlCLElBQU1uRSxJQUFJO2dCQUFFNEQsZUFBZSxDQUFDdEQsTUFBTXNELGFBQWE7WUFBQztJQUNuRSxJQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2xpYi9zdG9yZS50cz9hZDMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnXHJcbmltcG9ydCB7IFVzZXIsIE9yZ2FuaXphdGlvbiwgUHJvamVjdCwgQm9hcmQsIFRhc2ssIENoYXRTZXNzaW9uIH0gZnJvbSAnLi90eXBlcydcclxuXHJcbi8vIEF1dGggU3RvcmVcclxuaW50ZXJmYWNlIEF1dGhTdGF0ZSB7XHJcbiAgdXNlcjogVXNlciB8IG51bGxcclxuICBsb2FkaW5nOiBib29sZWFuXHJcbiAgc2V0VXNlcjogKHVzZXI6IFVzZXIgfCBudWxsKSA9PiB2b2lkXHJcbiAgc2V0TG9hZGluZzogKGxvYWRpbmc6IGJvb2xlYW4pID0+IHZvaWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUF1dGhTdG9yZSA9IGNyZWF0ZTxBdXRoU3RhdGU+KChzZXQpID0+ICh7XHJcbiAgdXNlcjogbnVsbCxcclxuICBsb2FkaW5nOiB0cnVlLFxyXG4gIHNldFVzZXI6ICh1c2VyKSA9PiBzZXQoeyB1c2VyIH0pLFxyXG4gIHNldExvYWRpbmc6IChsb2FkaW5nKSA9PiBzZXQoeyBsb2FkaW5nIH0pLFxyXG59KSlcclxuXHJcbi8vIE9yZ2FuaXphdGlvbiBTdG9yZVxyXG5pbnRlcmZhY2UgT3JnYW5pemF0aW9uU3RhdGUge1xyXG4gIGN1cnJlbnRPcmdhbml6YXRpb246IE9yZ2FuaXphdGlvbiB8IG51bGxcclxuICBvcmdhbml6YXRpb25zOiBPcmdhbml6YXRpb25bXVxyXG4gIHNldEN1cnJlbnRPcmdhbml6YXRpb246IChvcmc6IE9yZ2FuaXphdGlvbiB8IG51bGwpID0+IHZvaWRcclxuICBzZXRPcmdhbml6YXRpb25zOiAob3JnczogT3JnYW5pemF0aW9uW10pID0+IHZvaWRcclxuICBhZGRPcmdhbml6YXRpb246IChvcmc6IE9yZ2FuaXphdGlvbikgPT4gdm9pZFxyXG4gIHVwZGF0ZU9yZ2FuaXphdGlvbjogKG9yZzogT3JnYW5pemF0aW9uKSA9PiB2b2lkXHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VPcmdhbml6YXRpb25TdG9yZSA9IGNyZWF0ZTxPcmdhbml6YXRpb25TdGF0ZT4oKHNldCwgZ2V0KSA9PiAoe1xyXG4gIGN1cnJlbnRPcmdhbml6YXRpb246IG51bGwsXHJcbiAgb3JnYW5pemF0aW9uczogW10sXHJcbiAgc2V0Q3VycmVudE9yZ2FuaXphdGlvbjogKG9yZykgPT4gc2V0KHsgY3VycmVudE9yZ2FuaXphdGlvbjogb3JnIH0pLFxyXG4gIHNldE9yZ2FuaXphdGlvbnM6IChvcmdzKSA9PiBzZXQoeyBvcmdhbml6YXRpb25zOiBvcmdzIH0pLFxyXG4gIGFkZE9yZ2FuaXphdGlvbjogKG9yZykgPT4gc2V0KHsgb3JnYW5pemF0aW9uczogWy4uLmdldCgpLm9yZ2FuaXphdGlvbnMsIG9yZ10gfSksXHJcbiAgdXBkYXRlT3JnYW5pemF0aW9uOiAob3JnKSA9PiBzZXQoe1xyXG4gICAgb3JnYW5pemF0aW9uczogZ2V0KCkub3JnYW5pemF0aW9ucy5tYXAobyA9PiBvLmlkID09PSBvcmcuaWQgPyBvcmcgOiBvKSxcclxuICAgIGN1cnJlbnRPcmdhbml6YXRpb246IGdldCgpLmN1cnJlbnRPcmdhbml6YXRpb24/LmlkID09PSBvcmcuaWQgPyBvcmcgOiBnZXQoKS5jdXJyZW50T3JnYW5pemF0aW9uXHJcbiAgfSksXHJcbn0pKVxyXG5cclxuLy8gUHJvamVjdCBTdG9yZVxyXG5pbnRlcmZhY2UgUHJvamVjdFN0YXRlIHtcclxuICBjdXJyZW50UHJvamVjdDogUHJvamVjdCB8IG51bGxcclxuICBwcm9qZWN0czogUHJvamVjdFtdXHJcbiAgc2V0Q3VycmVudFByb2plY3Q6IChwcm9qZWN0OiBQcm9qZWN0IHwgbnVsbCkgPT4gdm9pZFxyXG4gIHNldFByb2plY3RzOiAocHJvamVjdHM6IFByb2plY3RbXSkgPT4gdm9pZFxyXG4gIGFkZFByb2plY3Q6IChwcm9qZWN0OiBQcm9qZWN0KSA9PiB2b2lkXHJcbiAgdXBkYXRlUHJvamVjdDogKHByb2plY3Q6IFByb2plY3QpID0+IHZvaWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZVByb2plY3RTdG9yZSA9IGNyZWF0ZTxQcm9qZWN0U3RhdGU+KChzZXQsIGdldCkgPT4gKHtcclxuICBjdXJyZW50UHJvamVjdDogbnVsbCxcclxuICBwcm9qZWN0czogW10sXHJcbiAgc2V0Q3VycmVudFByb2plY3Q6IChwcm9qZWN0KSA9PiBzZXQoeyBjdXJyZW50UHJvamVjdDogcHJvamVjdCB9KSxcclxuICBzZXRQcm9qZWN0czogKHByb2plY3RzKSA9PiBzZXQoeyBwcm9qZWN0cyB9KSxcclxuICBhZGRQcm9qZWN0OiAocHJvamVjdCkgPT4gc2V0KHsgcHJvamVjdHM6IFsuLi5nZXQoKS5wcm9qZWN0cywgcHJvamVjdF0gfSksXHJcbiAgdXBkYXRlUHJvamVjdDogKHByb2plY3QpID0+IHNldCh7XHJcbiAgICBwcm9qZWN0czogZ2V0KCkucHJvamVjdHMubWFwKHAgPT4gcC5pZCA9PT0gcHJvamVjdC5pZCA/IHByb2plY3QgOiBwKSxcclxuICAgIGN1cnJlbnRQcm9qZWN0OiBnZXQoKS5jdXJyZW50UHJvamVjdD8uaWQgPT09IHByb2plY3QuaWQgPyBwcm9qZWN0IDogZ2V0KCkuY3VycmVudFByb2plY3RcclxuICB9KSxcclxufSkpXHJcblxyXG4vLyBCb2FyZCBTdG9yZVxyXG5pbnRlcmZhY2UgQm9hcmRTdGF0ZSB7XHJcbiAgY3VycmVudEJvYXJkOiBCb2FyZCB8IG51bGxcclxuICBib2FyZHM6IEJvYXJkW11cclxuICBzZXRDdXJyZW50Qm9hcmQ6IChib2FyZDogQm9hcmQgfCBudWxsKSA9PiB2b2lkXHJcbiAgc2V0Qm9hcmRzOiAoYm9hcmRzOiBCb2FyZFtdKSA9PiB2b2lkXHJcbiAgYWRkQm9hcmQ6IChib2FyZDogQm9hcmQpID0+IHZvaWRcclxuICB1cGRhdGVCb2FyZDogKGJvYXJkOiBCb2FyZCkgPT4gdm9pZFxyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXNlQm9hcmRTdG9yZSA9IGNyZWF0ZTxCb2FyZFN0YXRlPigoc2V0LCBnZXQpID0+ICh7XHJcbiAgY3VycmVudEJvYXJkOiBudWxsLFxyXG4gIGJvYXJkczogW10sXHJcbiAgc2V0Q3VycmVudEJvYXJkOiAoYm9hcmQpID0+IHNldCh7IGN1cnJlbnRCb2FyZDogYm9hcmQgfSksXHJcbiAgc2V0Qm9hcmRzOiAoYm9hcmRzKSA9PiBzZXQoeyBib2FyZHMgfSksXHJcbiAgYWRkQm9hcmQ6IChib2FyZCkgPT4gc2V0KHsgYm9hcmRzOiBbLi4uZ2V0KCkuYm9hcmRzLCBib2FyZF0gfSksXHJcbiAgdXBkYXRlQm9hcmQ6IChib2FyZCkgPT4gc2V0KHtcclxuICAgIGJvYXJkczogZ2V0KCkuYm9hcmRzLm1hcChiID0+IGIuaWQgPT09IGJvYXJkLmlkID8gYm9hcmQgOiBiKSxcclxuICAgIGN1cnJlbnRCb2FyZDogZ2V0KCkuY3VycmVudEJvYXJkPy5pZCA9PT0gYm9hcmQuaWQgPyBib2FyZCA6IGdldCgpLmN1cnJlbnRCb2FyZFxyXG4gIH0pLFxyXG59KSlcclxuXHJcbi8vIFRhc2sgU3RvcmVcclxuaW50ZXJmYWNlIFRhc2tTdGF0ZSB7XHJcbiAgdGFza3M6IFRhc2tbXVxyXG4gIHNlbGVjdGVkVGFzazogVGFzayB8IG51bGxcclxuICBzZXRUYXNrczogKHRhc2tzOiBUYXNrW10pID0+IHZvaWRcclxuICBzZXRTZWxlY3RlZFRhc2s6ICh0YXNrOiBUYXNrIHwgbnVsbCkgPT4gdm9pZFxyXG4gIGFkZFRhc2s6ICh0YXNrOiBUYXNrKSA9PiB2b2lkXHJcbiAgdXBkYXRlVGFzazogKHRhc2s6IFRhc2spID0+IHZvaWRcclxuICByZW1vdmVUYXNrOiAodGFza0lkOiBzdHJpbmcpID0+IHZvaWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZVRhc2tTdG9yZSA9IGNyZWF0ZTxUYXNrU3RhdGU+KChzZXQsIGdldCkgPT4gKHtcclxuICB0YXNrczogW10sXHJcbiAgc2VsZWN0ZWRUYXNrOiBudWxsLFxyXG4gIHNldFRhc2tzOiAodGFza3MpID0+IHNldCh7IHRhc2tzIH0pLFxyXG4gIHNldFNlbGVjdGVkVGFzazogKHRhc2spID0+IHNldCh7IHNlbGVjdGVkVGFzazogdGFzayB9KSxcclxuICBhZGRUYXNrOiAodGFzaykgPT4gc2V0KHsgdGFza3M6IFsuLi5nZXQoKS50YXNrcywgdGFza10gfSksXHJcbiAgdXBkYXRlVGFzazogKHRhc2spID0+IHNldCh7XHJcbiAgICB0YXNrczogZ2V0KCkudGFza3MubWFwKHQgPT4gdC5pZCA9PT0gdGFzay5pZCA/IHRhc2sgOiB0KSxcclxuICAgIHNlbGVjdGVkVGFzazogZ2V0KCkuc2VsZWN0ZWRUYXNrPy5pZCA9PT0gdGFzay5pZCA/IHRhc2sgOiBnZXQoKS5zZWxlY3RlZFRhc2tcclxuICB9KSxcclxuICByZW1vdmVUYXNrOiAodGFza0lkKSA9PiBzZXQoe1xyXG4gICAgdGFza3M6IGdldCgpLnRhc2tzLmZpbHRlcih0ID0+IHQuaWQgIT09IHRhc2tJZCksXHJcbiAgICBzZWxlY3RlZFRhc2s6IGdldCgpLnNlbGVjdGVkVGFzaz8uaWQgPT09IHRhc2tJZCA/IG51bGwgOiBnZXQoKS5zZWxlY3RlZFRhc2tcclxuICB9KSxcclxufSkpXHJcblxyXG4vLyBDaGF0IFN0b3JlXHJcbmludGVyZmFjZSBDaGF0U3RhdGUge1xyXG4gIGN1cnJlbnRTZXNzaW9uOiBDaGF0U2Vzc2lvbiB8IG51bGxcclxuICBzZXNzaW9uczogQ2hhdFNlc3Npb25bXVxyXG4gIGlzTG9hZGluZzogYm9vbGVhblxyXG4gIHNldEN1cnJlbnRTZXNzaW9uOiAoc2Vzc2lvbjogQ2hhdFNlc3Npb24gfCBudWxsKSA9PiB2b2lkXHJcbiAgc2V0U2Vzc2lvbnM6IChzZXNzaW9uczogQ2hhdFNlc3Npb25bXSkgPT4gdm9pZFxyXG4gIGFkZFNlc3Npb246IChzZXNzaW9uOiBDaGF0U2Vzc2lvbikgPT4gdm9pZFxyXG4gIHVwZGF0ZVNlc3Npb246IChzZXNzaW9uOiBDaGF0U2Vzc2lvbikgPT4gdm9pZFxyXG4gIHNldExvYWRpbmc6IChsb2FkaW5nOiBib29sZWFuKSA9PiB2b2lkXHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VDaGF0U3RvcmUgPSBjcmVhdGU8Q2hhdFN0YXRlPigoc2V0LCBnZXQpID0+ICh7XHJcbiAgY3VycmVudFNlc3Npb246IG51bGwsXHJcbiAgc2Vzc2lvbnM6IFtdLFxyXG4gIGlzTG9hZGluZzogZmFsc2UsXHJcbiAgc2V0Q3VycmVudFNlc3Npb246IChzZXNzaW9uKSA9PiBzZXQoeyBjdXJyZW50U2Vzc2lvbjogc2Vzc2lvbiB9KSxcclxuICBzZXRTZXNzaW9uczogKHNlc3Npb25zKSA9PiBzZXQoeyBzZXNzaW9ucyB9KSxcclxuICBhZGRTZXNzaW9uOiAoc2Vzc2lvbikgPT4gc2V0KHsgc2Vzc2lvbnM6IFsuLi5nZXQoKS5zZXNzaW9ucywgc2Vzc2lvbl0gfSksXHJcbiAgdXBkYXRlU2Vzc2lvbjogKHNlc3Npb24pID0+IHNldCh7XHJcbiAgICBzZXNzaW9uczogZ2V0KCkuc2Vzc2lvbnMubWFwKHMgPT4gcy5pZCA9PT0gc2Vzc2lvbi5pZCA/IHNlc3Npb24gOiBzKSxcclxuICAgIGN1cnJlbnRTZXNzaW9uOiBnZXQoKS5jdXJyZW50U2Vzc2lvbj8uaWQgPT09IHNlc3Npb24uaWQgPyBzZXNzaW9uIDogZ2V0KCkuY3VycmVudFNlc3Npb25cclxuICB9KSxcclxuICBzZXRMb2FkaW5nOiAobG9hZGluZykgPT4gc2V0KHsgaXNMb2FkaW5nOiBsb2FkaW5nIH0pLFxyXG59KSlcclxuXHJcbi8vIFVJIFN0b3JlXHJcbmludGVyZmFjZSBVSVN0YXRlIHtcclxuICBzaWRlYmFyT3BlbjogYm9vbGVhblxyXG4gIGNoYXRQYW5lbE9wZW46IGJvb2xlYW5cclxuICB0YXNrTW9kYWxPcGVuOiBib29sZWFuXHJcbiAgc2V0U2lkZWJhck9wZW46IChvcGVuOiBib29sZWFuKSA9PiB2b2lkXHJcbiAgc2V0Q2hhdFBhbmVsT3BlbjogKG9wZW46IGJvb2xlYW4pID0+IHZvaWRcclxuICBzZXRUYXNrTW9kYWxPcGVuOiAob3BlbjogYm9vbGVhbikgPT4gdm9pZFxyXG4gIHRvZ2dsZVNpZGViYXI6ICgpID0+IHZvaWRcclxuICB0b2dnbGVDaGF0UGFuZWw6ICgpID0+IHZvaWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHVzZVVJU3RvcmUgPSBjcmVhdGU8VUlTdGF0ZT4oKHNldCwgZ2V0KSA9PiAoe1xyXG4gIHNpZGViYXJPcGVuOiB0cnVlLFxyXG4gIGNoYXRQYW5lbE9wZW46IGZhbHNlLFxyXG4gIHRhc2tNb2RhbE9wZW46IGZhbHNlLFxyXG4gIHNldFNpZGViYXJPcGVuOiAob3BlbikgPT4gc2V0KHsgc2lkZWJhck9wZW46IG9wZW4gfSksXHJcbiAgc2V0Q2hhdFBhbmVsT3BlbjogKG9wZW4pID0+IHNldCh7IGNoYXRQYW5lbE9wZW46IG9wZW4gfSksXHJcbiAgc2V0VGFza01vZGFsT3BlbjogKG9wZW4pID0+IHNldCh7IHRhc2tNb2RhbE9wZW46IG9wZW4gfSksXHJcbiAgdG9nZ2xlU2lkZWJhcjogKCkgPT4gc2V0KHsgc2lkZWJhck9wZW46ICFnZXQoKS5zaWRlYmFyT3BlbiB9KSxcclxuICB0b2dnbGVDaGF0UGFuZWw6ICgpID0+IHNldCh7IGNoYXRQYW5lbE9wZW46ICFnZXQoKS5jaGF0UGFuZWxPcGVuIH0pLFxyXG59KSkiXSwibmFtZXMiOlsiY3JlYXRlIiwidXNlQXV0aFN0b3JlIiwic2V0IiwidXNlciIsImxvYWRpbmciLCJzZXRVc2VyIiwic2V0TG9hZGluZyIsInVzZU9yZ2FuaXphdGlvblN0b3JlIiwiZ2V0IiwiY3VycmVudE9yZ2FuaXphdGlvbiIsIm9yZ2FuaXphdGlvbnMiLCJzZXRDdXJyZW50T3JnYW5pemF0aW9uIiwib3JnIiwic2V0T3JnYW5pemF0aW9ucyIsIm9yZ3MiLCJhZGRPcmdhbml6YXRpb24iLCJ1cGRhdGVPcmdhbml6YXRpb24iLCJtYXAiLCJvIiwiaWQiLCJ1c2VQcm9qZWN0U3RvcmUiLCJjdXJyZW50UHJvamVjdCIsInByb2plY3RzIiwic2V0Q3VycmVudFByb2plY3QiLCJwcm9qZWN0Iiwic2V0UHJvamVjdHMiLCJhZGRQcm9qZWN0IiwidXBkYXRlUHJvamVjdCIsInAiLCJ1c2VCb2FyZFN0b3JlIiwiY3VycmVudEJvYXJkIiwiYm9hcmRzIiwic2V0Q3VycmVudEJvYXJkIiwiYm9hcmQiLCJzZXRCb2FyZHMiLCJhZGRCb2FyZCIsInVwZGF0ZUJvYXJkIiwiYiIsInVzZVRhc2tTdG9yZSIsInRhc2tzIiwic2VsZWN0ZWRUYXNrIiwic2V0VGFza3MiLCJzZXRTZWxlY3RlZFRhc2siLCJ0YXNrIiwiYWRkVGFzayIsInVwZGF0ZVRhc2siLCJ0IiwicmVtb3ZlVGFzayIsInRhc2tJZCIsImZpbHRlciIsInVzZUNoYXRTdG9yZSIsImN1cnJlbnRTZXNzaW9uIiwic2Vzc2lvbnMiLCJpc0xvYWRpbmciLCJzZXRDdXJyZW50U2Vzc2lvbiIsInNlc3Npb24iLCJzZXRTZXNzaW9ucyIsImFkZFNlc3Npb24iLCJ1cGRhdGVTZXNzaW9uIiwicyIsInVzZVVJU3RvcmUiLCJzaWRlYmFyT3BlbiIsImNoYXRQYW5lbE9wZW4iLCJ0YXNrTW9kYWxPcGVuIiwic2V0U2lkZWJhck9wZW4iLCJvcGVuIiwic2V0Q2hhdFBhbmVsT3BlbiIsInNldFRhc2tNb2RhbE9wZW4iLCJ0b2dnbGVTaWRlYmFyIiwidG9nZ2xlQ2hhdFBhbmVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"49997f4810d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kNzM0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDk5OTdmNDgxMGQ0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthProvider */ \"(rsc)/./src/components/auth/AuthProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI Productivity Platform\",\n    description: \"Modern productivity platform with AI superpowers\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ21DO0FBSWxELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MscUVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcclxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgQXV0aFByb3ZpZGVyIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL0F1dGhQcm92aWRlcidcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdBSSBQcm9kdWN0aXZpdHkgUGxhdGZvcm0nLFxyXG4gIGRlc2NyaXB0aW9uOiAnTW9kZXJuIHByb2R1Y3Rpdml0eSBwbGF0Zm9ybSB3aXRoIEFJIHN1cGVycG93ZXJzJyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cclxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59Il0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/setup/page.tsx":
/*!********************************!*\
  !*** ./src/app/setup/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\app\setup\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\components\auth\AuthProvider.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/next","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();