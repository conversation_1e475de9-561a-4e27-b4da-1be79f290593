# Supabase Setup Guide

## Quick Setup for FocusFlow AI

This application has been migrated from Firebase to Supabase. Follow these steps to set up your Supabase database:

### Step 1: Create Supabase Project

1. **Go to [supabase.com](https://supabase.com)** and create a new account if you don't have one
2. **Create a new project**:
   - Click "New Project"
   - Choose your organization
   - Enter project name: "FocusFlow AI"
   - Enter database password (save this!)
   - Select region closest to you
   - Click "Create new project"

### Step 2: Set Up Database Schema

1. **Go to SQL Editor** in your Supabase dashboard
2. **Copy and paste the entire contents** of `supabase-schema.sql` file
3. **Click "Run"** to execute the SQL
4. **Verify tables were created** by going to "Table Editor"

You should see these tables:
- `user_profiles`
- `organizations`
- `projects`
- `project_members`
- `boards`
- `tasks`
- `notes`
- `chat_sessions`

### Step 3: Configure Environment Variables

1. **Get your project credentials**:
   - Go to Settings > API
   - Copy "Project URL"
   - Copy "anon public" key
   - Copy "service_role secret" key

2. **Update your `.env.local` file**:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

   # OpenRouter API (for AI chat)
   OPENROUTER_API_KEY=your_openrouter_api_key

   # Site URL
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   ```

### Step 4: Enable Authentication (Optional)

1. **Go to Authentication > Settings** in Supabase dashboard
2. **Enable Google OAuth** (optional):
   - Go to Authentication > Providers
   - Enable Google provider
   - Add your Google OAuth credentials

### Step 5: Test the Application

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Open http://localhost:3000**

3. **Register the first user**:
   - The first user automatically becomes admin
   - Creates default organization
   - Sets up initial data

### Troubleshooting

#### "Invalid API key" Error
- Double-check your environment variables
- Make sure you're using the correct project URL and keys
- Restart your development server after changing .env.local

#### "Permission denied" Error
- The SQL schema includes Row Level Security (RLS) policies
- Make sure you ran the complete `supabase-schema.sql` file
- Check that all tables were created properly

#### Authentication Issues
- Make sure your site URL is configured correctly
- For Google OAuth, verify your OAuth credentials
- Check that email confirmation is disabled for development

### Database Schema Overview

The application uses these main tables:

- **user_profiles**: User accounts and preferences
- **organizations**: Company/team organizations
- **projects**: Individual projects within organizations
- **project_members**: Project membership and roles
- **boards**: Kanban boards for projects
- **tasks**: Individual tasks with status, priority, etc.
- **notes**: User notes and documentation
- **chat_sessions**: AI chat conversations

All tables include:
- Automatic timestamps (created_at, updated_at)
- Row Level Security (RLS) policies
- Proper foreign key relationships
- Indexes for performance

### Next Steps

Once your database is set up:

1. **Create your first project**
2. **Set up a Kanban board**
3. **Add some tasks**
4. **Try the AI chat feature**
5. **Explore notes and calendar features**

For production deployment, make sure to:
- Use strong database passwords
- Configure proper OAuth redirect URLs
- Set up email templates for authentication
- Enable email confirmation for new users
