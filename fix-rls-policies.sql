-- Fix for RLS policies that prevent admin user creation
-- Run this in your Supabase SQL Editor to fix the issue

-- Temporarily disable <PERSON><PERSON> to allow admin user creation
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies for user_profiles
DROP POLICY IF EXISTS "Users can view all profiles in their organization" ON user_profiles;
DROP POLICY IF EXISTS "Users can view profiles in their organization" ON user_profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;

-- Create new, permissive policies for development
CREATE POLICY "Allow all operations on user_profiles" ON user_profiles FOR ALL USING (true) WITH CHECK (true);

-- Re-enable RLS with the permissive policy
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
