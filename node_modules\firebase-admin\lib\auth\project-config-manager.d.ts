/*! firebase-admin v13.4.0 */
import { ProjectConfig, UpdateProjectConfigRequest } from './project-config';
/**
 * Manages (gets and updates) the current project config.
 */
export declare class ProjectConfigManager {
    private readonly authRequestHandler;
    /**
     * Get the project configuration.
     *
     * @returns A promise fulfilled with the project configuration.
     */
    getProjectConfig(): Promise<ProjectConfig>;
    /**
     * Updates an existing project configuration.
     *
     * @param projectConfigOptions - The properties to update on the project.
     *
     * @returns A promise fulfilled with the updated project config.
     */
    updateProjectConfig(projectConfigOptions: UpdateProjectConfigRequest): Promise<ProjectConfig>;
}
