import { createClient } from '@supabase/supabase-js'

// Types for our database
export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          description: string | null
          owner_id: string
          settings: any
          api_keys: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          owner_id: string
          settings?: any
          api_keys?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          owner_id?: string
          settings?: any
          api_keys?: any
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          email: string
          display_name: string
          photo_url: string | null
          role: 'admin' | 'user'
          organization_id: string | null
          preferences: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          display_name: string
          photo_url?: string | null
          role?: 'admin' | 'user'
          organization_id?: string | null
          preferences?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          display_name?: string
          photo_url?: string | null
          role?: 'admin' | 'user'
          organization_id?: string | null
          preferences?: any
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          organization_id: string
          owner_id: string
          settings: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          organization_id: string
          owner_id: string
          settings?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          organization_id?: string
          owner_id?: string
          settings?: any
          created_at?: string
          updated_at?: string
        }
      }
      project_members: {
        Row: {
          id: string
          project_id: string
          user_id: string
          role: string
          added_at: string
        }
        Insert: {
          id?: string
          project_id: string
          user_id: string
          role?: string
          added_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          user_id?: string
          role?: string
          added_at?: string
        }
      }
      boards: {
        Row: {
          id: string
          name: string
          description: string | null
          project_id: string
          organization_id: string
          columns: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          project_id: string
          organization_id: string
          columns?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          project_id?: string
          organization_id?: string
          columns?: any
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string | null
          status: 'todo' | 'in-progress' | 'done'
          priority: 'low' | 'medium' | 'high'
          assignee_id: string | null
          project_id: string
          board_id: string
          column_id: string | null
          position: number
          due_date: string | null
          tags: string[]
          subtasks: any
          time_tracking: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          status?: 'todo' | 'in-progress' | 'done'
          priority?: 'low' | 'medium' | 'high'
          assignee_id?: string | null
          project_id: string
          board_id: string
          column_id?: string | null
          position?: number
          due_date?: string | null
          tags?: string[]
          subtasks?: any
          time_tracking?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          status?: 'todo' | 'in-progress' | 'done'
          priority?: 'low' | 'medium' | 'high'
          assignee_id?: string | null
          project_id?: string
          board_id?: string
          column_id?: string | null
          position?: number
          due_date?: string | null
          tags?: string[]
          subtasks?: any
          time_tracking?: any
          created_at?: string
          updated_at?: string
        }
      }
      notes: {
        Row: {
          id: string
          title: string
          content: string | null
          excerpt: string | null
          starred: boolean
          folder_id: string | null
          tags: string[]
          user_id: string
          organization_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content?: string | null
          excerpt?: string | null
          starred?: boolean
          folder_id?: string | null
          tags?: string[]
          user_id: string
          organization_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string | null
          excerpt?: string | null
          starred?: boolean
          folder_id?: string | null
          tags?: string[]
          user_id?: string
          organization_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      chat_sessions: {
        Row: {
          id: string
          title: string | null
          user_id: string
          messages: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title?: string | null
          user_id: string
          messages?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string | null
          user_id?: string
          messages?: any
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client for use in client components
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Client component helper
export const createSupabaseClient = () => createClient<Database>(supabaseUrl, supabaseAnonKey)

// Admin client for server-side operations
export const createSupabaseAdminClient = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export default supabase
