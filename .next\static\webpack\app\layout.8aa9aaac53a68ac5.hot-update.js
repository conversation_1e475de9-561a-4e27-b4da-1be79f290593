"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"0e97bf963ea2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YzY2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBlOTdiZjk2M2VhMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthProvider: Setting up auth state listener\");\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (firebaseUser)=>{\n            console.log(\"AuthProvider: Auth state changed:\", firebaseUser ? firebaseUser.uid : \"null\");\n            setLoading(true);\n            if (firebaseUser) {\n                try {\n                    // Get user document from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        setUser(userData);\n                    } else {\n                        // User exists in Firebase Auth but not in Firestore\n                        // Create the user document automatically\n                        console.log(\"Creating missing user document for:\", firebaseUser.uid);\n                        const newUser = {\n                            id: firebaseUser.uid,\n                            email: firebaseUser.email || \"\",\n                            displayName: firebaseUser.displayName || \"\",\n                            photoURL: firebaseUser.photoURL || undefined,\n                            role: \"user\",\n                            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.Timestamp.now(),\n                            preferences: {\n                                theme: \"light\",\n                                notifications: true\n                            }\n                        };\n                        try {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", firebaseUser.uid), newUser);\n                            setUser(newUser);\n                            console.log(\"User document created successfully\");\n                        } catch (error) {\n                            console.error(\"Error creating user document:\", error);\n                            setUser(null);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user data:\", error);\n                    setUser(null);\n                }\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthProvider, \"8r5jpERPlh6EEwNWWdDJSIR0A9c=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useAuthStore\n    ];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/AuthProvider.tsx\n"));

/***/ })

});