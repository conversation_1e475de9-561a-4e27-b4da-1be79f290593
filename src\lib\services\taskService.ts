import { createSupabaseClient } from '@/lib/supabase'
import { Task } from '@/lib/types'

export class TaskService {
  /**
   * Create a new task
   */
  static async createTask(taskData: {
    title: string
    description?: string
    status?: 'todo' | 'in-progress' | 'done'
    priority?: 'low' | 'medium' | 'high'
    assignee_id?: string
    project_id: string
    board_id: string
    column_id?: string
    position?: number
    due_date?: string
    tags?: string[]
    time_tracking?: { estimated: number; logged: number }
  }): Promise<Task> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .insert([{
        title: taskData.title,
        description: taskData.description || '',
        status: taskData.status || 'todo',
        priority: taskData.priority || 'medium',
        assignee_id: taskData.assignee_id,
        project_id: taskData.project_id,
        board_id: taskData.board_id,
        column_id: taskData.column_id,
        position: taskData.position || 0,
        due_date: taskData.due_date,
        tags: taskData.tags || [],
        subtasks: [],
        time_tracking: taskData.time_tracking || { estimated: 0, logged: 0 }
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating task:', error)
      throw new Error(error.message)
    }

    return data as Task
  }

  /**
   * Get all tasks for a project
   */
  static async getProjectTasks(projectId: string): Promise<Task[]> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:user_profiles(id, display_name, email, photo_url)
      `)
      .eq('project_id', projectId)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching project tasks:', error)
      throw new Error(error.message)
    }

    return data as Task[]
  }

  /**
   * Get all tasks for a board
   */
  static async getBoardTasks(boardId: string): Promise<Task[]> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:user_profiles(id, display_name, email, photo_url)
      `)
      .eq('board_id', boardId)
      .order('position', { ascending: true })

    if (error) {
      console.error('Error fetching board tasks:', error)
      throw new Error(error.message)
    }

    return data as Task[]
  }

  /**
   * Get tasks assigned to a user
   */
  static async getUserTasks(userId: string): Promise<Task[]> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:user_profiles(id, display_name, email, photo_url),
        project:projects(id, name)
      `)
      .eq('assignee_id', userId)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching user tasks:', error)
      throw new Error(error.message)
    }

    return data as Task[]
  }

  /**
   * Get a single task by ID
   */
  static async getTask(taskId: string): Promise<Task | null> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assignee:user_profiles(id, display_name, email, photo_url)
      `)
      .eq('id', taskId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Task not found
      }
      console.error('Error fetching task:', error)
      throw new Error(error.message)
    }

    return data as Task
  }

  /**
   * Update a task
   */
  static async updateTask(taskId: string, updates: Partial<Task>): Promise<Task> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', taskId)
      .select()
      .single()

    if (error) {
      console.error('Error updating task:', error)
      throw new Error(error.message)
    }

    return data as Task
  }

  /**
   * Delete a task
   */
  static async deleteTask(taskId: string): Promise<void> {
    const supabase = createSupabaseClient()
    
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId)

    if (error) {
      console.error('Error deleting task:', error)
      throw new Error(error.message)
    }
  }

  /**
   * Move task to different column
   */
  static async moveTask(taskId: string, columnId: string, position: number): Promise<Task> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .update({ 
        column_id: columnId, 
        position,
        status: columnId === 'col-1' ? 'todo' : columnId === 'col-2' ? 'in-progress' : 'done'
      })
      .eq('id', taskId)
      .select()
      .single()

    if (error) {
      console.error('Error moving task:', error)
      throw new Error(error.message)
    }

    return data as Task
  }

  /**
   * Update task time tracking
   */
  static async updateTimeTracking(taskId: string, timeTracking: { estimated: number; logged: number }): Promise<Task> {
    const supabase = createSupabaseClient()
    
    const { data, error } = await supabase
      .from('tasks')
      .update({ time_tracking: timeTracking })
      .eq('id', taskId)
      .select()
      .single()

    if (error) {
      console.error('Error updating time tracking:', error)
      throw new Error(error.message)
    }

    return data as Task
  }
}
