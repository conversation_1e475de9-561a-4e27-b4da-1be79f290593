import { createSupabaseClient } from '@/lib/supabase'
import { User, Organization } from '@/lib/types'

export class SetupService {
  /**
   * Check if this is the first user (admin) in the system
   */
  static async isFirstUser(): Promise<boolean> {
    try {
      const supabase = createSupabaseClient()
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })

      if (error) {
        console.error('Error checking if first user:', error)
        return false
      }

      return count === 0
    } catch (error) {
      console.error('Error checking if first user:', error)
      return false
    }
  }

  /**
   * Create the admin user and default organization
   */
  static async createAdminUser(supabaseUser: any, displayName: string): Promise<{ user: User; organization: Organization }> {
    try {
      const supabase = createSupabaseClient()

      // Create admin user profile
      const adminUserData = {
        id: supabaseUser.id,
        email: supabaseUser.email,
        display_name: displayName || supabaseUser.user_metadata?.display_name || 'Admin',
        photo_url: supabaseUser.user_metadata?.avatar_url,
        role: 'admin' as const,
        preferences: {
          theme: 'light' as const,
          notifications: true
        }
      }

      const { data: adminUser, error: userError } = await supabase
        .from('user_profiles')
        .insert([adminUserData])
        .select()
        .single()

      if (userError) throw userError

      // Create default organization
      const orgData = {
        name: `${adminUserData.display_name}'s Organization`,
        description: 'Default organization for the admin user',
        owner_id: supabaseUser.id,
        settings: {
          aiModels: {
            'anthropic/claude-3-haiku': {
              enabled: true,
              displayName: 'Claude 3 Haiku (Fast)',
              provider: 'openrouter'
            },
            'anthropic/claude-3-sonnet': {
              enabled: true,
              displayName: 'Claude 3 Sonnet (Balanced)',
              provider: 'openrouter'
            },
            'openai/gpt-4': {
              enabled: true,
              displayName: 'GPT-4 (Advanced)',
              provider: 'openrouter'
            },
            'openai/gpt-3.5-turbo': {
              enabled: true,
              displayName: 'GPT-3.5 Turbo (Fast)',
              provider: 'openrouter'
            }
          },
          mcpEnabled: false
        },
        api_keys: {
          openrouter: []
        }
      }

      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .insert([orgData])
        .select()
        .single()

      if (orgError) throw orgError

      // Update user with organization_id
      const { data: updatedUser, error: updateError } = await supabase
        .from('user_profiles')
        .update({ organization_id: organization.id })
        .eq('id', supabaseUser.id)
        .select()
        .single()

      if (updateError) throw updateError

      return { user: updatedUser as User, organization: organization as Organization }
    } catch (error: any) {
      console.error('Error creating admin user:', error)
      throw new Error(`Failed to create admin user: ${error.message}`)
    }
  }

  /**
   * Check if the system has been initialized
   */
  static async isSystemInitialized(): Promise<boolean> {
    try {
      const supabase = createSupabaseClient()
      const { count, error } = await supabase
        .from('organizations')
        .select('*', { count: 'exact', head: true })

      if (error) {
        console.error('Error checking system initialization:', error)
        return false
      }

      return (count || 0) > 0
    } catch (error) {
      console.error('Error checking system initialization:', error)
      return false
    }
  }

  /**
   * Get the first organization (default organization)
   */
  static async getDefaultOrganization(): Promise<Organization | null> {
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .limit(1)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // No organizations found
        }
        console.error('Error getting default organization:', error)
        return null
      }

      return data as Organization
    } catch (error) {
      console.error('Error getting default organization:', error)
      return null
    }
  }

  /**
   * Create a regular user (non-admin)
   */
  static async createRegularUser(supabaseUser: any, displayName: string): Promise<User> {
    try {
      const supabase = createSupabaseClient()

      // Get the default organization
      const defaultOrg = await this.getDefaultOrganization()

      const userData = {
        id: supabaseUser.id,
        email: supabaseUser.email,
        display_name: displayName || supabaseUser.user_metadata?.display_name || '',
        photo_url: supabaseUser.user_metadata?.avatar_url,
        role: 'user' as const,
        organization_id: defaultOrg?.id,
        preferences: {
          theme: 'light' as const,
          notifications: true
        }
      }

      const { data: user, error } = await supabase
        .from('user_profiles')
        .insert([userData])
        .select()
        .single()

      if (error) throw error

      return user as User
    } catch (error: any) {
      console.error('Error creating regular user:', error)
      throw new Error(`Failed to create user: ${error.message}`)
    }
  }
}