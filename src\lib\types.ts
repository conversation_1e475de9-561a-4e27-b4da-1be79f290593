// User Types
export interface User {
  id: string
  email: string
  display_name: string
  photo_url?: string
  role: 'admin' | 'user'
  organization_id?: string
  created_at: string
  updated_at: string
  preferences: {
    theme: 'light' | 'dark'
    notifications: boolean
  }
}

// Organization Types
export interface Organization {
  id: string
  name: string
  description?: string
  owner_id: string
  settings: {
    aiModels?: {
      [modelId: string]: {
        enabled: boolean
        displayName: string
        provider: 'openrouter'
      }
    }
    mcpEnabled?: boolean
  }
  api_keys: {
    openrouter?: string[]
  }
  created_at: string
  updated_at: string
}

// Project Types
export interface Project {
  id: string
  name: string
  description?: string
  organization_id: string
  owner_id: string
  settings: {
    visibility: 'private' | 'organization'
  }
  created_at: string
  updated_at: string
}

// Project Members (separate table in Supabase)
export interface ProjectMember {
  id: string
  project_id: string
  user_id: string
  role: 'admin' | 'member'
  added_at: string
}

// Board Types
export interface BoardColumn {
  id: string
  title: string
  position: number
  taskIds: string[]
}

export interface Board {
  id: string
  name: string
  description?: string
  project_id: string
  organization_id: string
  columns: {
    [columnId: string]: BoardColumn
  }
  created_at: string
  updated_at: string
}

// Task Types
export interface Subtask {
  id: string
  title: string
  completed: boolean
  created_at: string
}

export interface Task {
  id: string
  title: string
  description?: string
  board_id: string
  column_id?: string
  project_id: string
  assignee_id?: string
  priority: 'low' | 'medium' | 'high'
  status: 'todo' | 'in-progress' | 'done'
  due_date?: string
  tags: string[]
  position: number
  subtasks: Subtask[]
  time_tracking: {
    estimated: number
    logged: number
  }
  created_at: string
  updated_at: string
}

// Note Types
export interface Note {
  id: string
  title: string
  content?: string
  excerpt?: string
  starred: boolean
  folder_id?: string
  tags: string[]
  user_id: string
  organization_id: string
  created_at: string
  updated_at: string
}

// AI Chat Types
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  actions?: {
    type: 'create_task' | 'create_project' | 'create_note'
    data: any
  }[]
}

export interface ChatSession {
  id: string
  title?: string
  user_id: string
  messages: any
  created_at: string
  updated_at: string
}