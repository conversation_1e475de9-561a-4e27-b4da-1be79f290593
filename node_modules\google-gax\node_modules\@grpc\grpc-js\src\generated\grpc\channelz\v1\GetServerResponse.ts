// Original file: proto/channelz.proto

import type { Server as _grpc_channelz_v1_Server, Server__Output as _grpc_channelz_v1_Server__Output } from '../../../grpc/channelz/v1/Server';

export interface GetServerResponse {
  /**
   * The Server that corresponds to the requested server_id.  This field
   * should be set.
   */
  'server'?: (_grpc_channelz_v1_Server | null);
}

export interface GetServerResponse__Output {
  /**
   * The Server that corresponds to the requested server_id.  This field
   * should be set.
   */
  'server': (_grpc_channelz_v1_Server__Output | null);
}
