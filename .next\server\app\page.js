/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/AuthProvider.tsx */ \"(ssr)/./src/components/auth/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NvZGUtZmlsZXMlNUMlNUNhaSUyMGZpbGVzJTVDJTVDdG8tZG8lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXV0aCU1QyU1Q0F1dGhQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcm9kdWN0aXZpdHktcGxhdGZvcm0vP2YwOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcY29kZS1maWxlc1xcXFxhaSBmaWxlc1xcXFx0by1kb1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhdXRoXFxcXEF1dGhQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjb2RlLWZpbGVzJTVDJTVDYWklMjBmaWxlcyU1QyU1Q3RvLWRvJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUF3RiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByb2R1Y3Rpdml0eS1wbGF0Zm9ybS8/NmNhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNvZGUtZmlsZXNcXFxcYWkgZmlsZXNcXFxcdG8tZG9cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccode-files%5C%5Cai%20files%5C%5Cto-do%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,SparklesIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _lib_services_setupService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/setupService */ \"(ssr)/./src/lib/services/setupService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HomePage() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFirstUser, setIsFirstUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkFirstUser = async ()=>{\n            try {\n                const firstUser = await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_4__.SetupService.isFirstUser();\n                if (firstUser) {\n                    // Redirect to setup page for first user\n                    router.push(\"/setup\");\n                    return;\n                }\n                setIsFirstUser(false);\n            } catch (error) {\n                console.error(\"Error checking first user:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkFirstUser();\n    }, [\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"AI Productivity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/auth/login\",\n                                        className: \"text-gray-600 hover:text-gray-900\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        className: \"btn-primary\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 sm:text-6xl\",\n                                children: [\n                                    \"Productivity Platform with\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-600\",\n                                        children: \" AI Superpowers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-6 text-lg text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Manage projects, collaborate with teams, and boost productivity with intelligent AI assistance. Create tasks, organize boards, and let AI help you stay on top of everything.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-10 flex justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            \"Start Free Trial\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"#features\",\n                                        className: \"btn-secondary\",\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"features\",\n                        className: \"mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-6 w-6 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Intelligent AI that helps create tasks, schedule events, and provides smart suggestions for your workflow.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Real-time collaboration with kanban boards, task assignments, and team communication features.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_SparklesIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-6 w-6 text-primary-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Project Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Organize projects with kanban boards, to-do lists, notes, and event scheduling all in one place.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"System Administrator?\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/admin-setup\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"Set up admin account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./src/lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthProvider({ children }) {\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseClient)();\n        // Get initial session\n        const getInitialSession = async ()=>{\n            setLoading(true);\n            try {\n                const { data: { session } } = await supabase.auth.getSession();\n                if (session?.user) {\n                    // Get user profile from our custom table\n                    const { data: profile, error } = await supabase.from(\"user_profiles\").select(\"*\").eq(\"id\", session.user.id).single();\n                    if (error && error.code !== \"PGRST116\") {\n                        console.error(\"Error fetching user profile:\", error);\n                        setUser(null);\n                    } else if (profile) {\n                        setUser(profile);\n                    } else {\n                        // Profile doesn't exist, it should be created by the trigger\n                        // But let's create it manually if needed\n                        const newProfile = {\n                            id: session.user.id,\n                            email: session.user.email || \"\",\n                            display_name: session.user.user_metadata?.display_name || session.user.email || \"\",\n                            photo_url: session.user.user_metadata?.avatar_url,\n                            role: \"user\",\n                            preferences: {\n                                theme: \"light\",\n                                notifications: true\n                            }\n                        };\n                        const { data: createdProfile, error: createError } = await supabase.from(\"user_profiles\").insert([\n                            newProfile\n                        ]).select().single();\n                        if (createError) {\n                            console.error(\"Error creating user profile:\", createError);\n                            setUser(null);\n                        } else {\n                            setUser(createdProfile);\n                        }\n                    }\n                } else {\n                    setUser(null);\n                }\n            } catch (error) {\n                console.error(\"Error in auth provider:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setLoading(true);\n            if (session?.user) {\n                // Get user profile\n                const { data: profile } = await supabase.from(\"user_profiles\").select(\"*\").eq(\"id\", session.user.id).single();\n                setUser(profile);\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/services/setupService.ts":
/*!******************************************!*\
  !*** ./src/lib/services/setupService.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SetupService: () => (/* binding */ SetupService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nclass SetupService {\n    /**\r\n   * Check if this is the first user (admin) in the system\r\n   */ static async isFirstUser() {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseClient)();\n            const { count, error } = await supabase.from(\"user_profiles\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            if (error) {\n                console.error(\"Error checking if first user:\", error);\n                return false;\n            }\n            return count === 0;\n        } catch (error) {\n            console.error(\"Error checking if first user:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Create the admin user and default organization\r\n   */ static async createAdminUser(supabaseUser, displayName) {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseClient)();\n            // Create admin user profile\n            const adminUserData = {\n                id: supabaseUser.id,\n                email: supabaseUser.email,\n                display_name: displayName || supabaseUser.user_metadata?.display_name || \"Admin\",\n                photo_url: supabaseUser.user_metadata?.avatar_url,\n                role: \"admin\",\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            const { data: adminUser, error: userError } = await supabase.from(\"user_profiles\").insert([\n                adminUserData\n            ]).select().single();\n            if (userError) throw userError;\n            // Create default organization\n            const orgData = {\n                name: `${adminUserData.display_name}'s Organization`,\n                description: \"Default organization for the admin user\",\n                owner_id: supabaseUser.id,\n                settings: {\n                    aiModels: {\n                        \"anthropic/claude-3-haiku\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Haiku (Fast)\",\n                            provider: \"openrouter\"\n                        },\n                        \"anthropic/claude-3-sonnet\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Sonnet (Balanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-4\": {\n                            enabled: true,\n                            displayName: \"GPT-4 (Advanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-3.5-turbo\": {\n                            enabled: true,\n                            displayName: \"GPT-3.5 Turbo (Fast)\",\n                            provider: \"openrouter\"\n                        }\n                    },\n                    mcpEnabled: false\n                },\n                api_keys: {\n                    openrouter: []\n                }\n            };\n            const { data: organization, error: orgError } = await supabase.from(\"organizations\").insert([\n                orgData\n            ]).select().single();\n            if (orgError) throw orgError;\n            // Update user with organization_id\n            const { data: updatedUser, error: updateError } = await supabase.from(\"user_profiles\").update({\n                organization_id: organization.id\n            }).eq(\"id\", supabaseUser.id).select().single();\n            if (updateError) throw updateError;\n            return {\n                user: updatedUser,\n                organization: organization\n            };\n        } catch (error) {\n            console.error(\"Error creating admin user:\", error);\n            throw new Error(`Failed to create admin user: ${error.message}`);\n        }\n    }\n    /**\r\n   * Check if the system has been initialized\r\n   */ static async isSystemInitialized() {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseClient)();\n            const { count, error } = await supabase.from(\"organizations\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            if (error) {\n                console.error(\"Error checking system initialization:\", error);\n                return false;\n            }\n            return (count || 0) > 0;\n        } catch (error) {\n            console.error(\"Error checking system initialization:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Get the first organization (default organization)\r\n   */ static async getDefaultOrganization() {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseClient)();\n            const { data, error } = await supabase.from(\"organizations\").select(\"*\").limit(1).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No organizations found\n                    ;\n                }\n                console.error(\"Error getting default organization:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting default organization:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * Create a regular user (non-admin)\r\n   */ static async createRegularUser(supabaseUser, displayName) {\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseClient)();\n            // Get the default organization\n            const defaultOrg = await this.getDefaultOrganization();\n            const userData = {\n                id: supabaseUser.id,\n                email: supabaseUser.email,\n                display_name: displayName || supabaseUser.user_metadata?.display_name || \"\",\n                photo_url: supabaseUser.user_metadata?.avatar_url,\n                role: \"user\",\n                organization_id: defaultOrg?.id,\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            const { data: user, error } = await supabase.from(\"user_profiles\").insert([\n                userData\n            ]).select().single();\n            if (error) throw error;\n            return user;\n        } catch (error) {\n            console.error(\"Error creating regular user:\", error);\n            throw new Error(`Failed to create user: ${error.message}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3NlcnZpY2VzL3NldHVwU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUc5QyxNQUFNQztJQUNYOztHQUVDLEdBQ0QsYUFBYUMsY0FBZ0M7UUFDM0MsSUFBSTtZQUNGLE1BQU1DLFdBQVdILG1FQUFvQkE7WUFDckMsTUFBTSxFQUFFSSxLQUFLLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1GLFNBQzVCRyxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxLQUFLO2dCQUFFSCxPQUFPO2dCQUFTSSxNQUFNO1lBQUs7WUFFNUMsSUFBSUgsT0FBTztnQkFDVEksUUFBUUosS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9DLE9BQU87WUFDVDtZQUVBLE9BQU9ELFVBQVU7UUFDbkIsRUFBRSxPQUFPQyxPQUFPO1lBQ2RJLFFBQVFKLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhSyxnQkFBZ0JDLFlBQWlCLEVBQUVDLFdBQW1CLEVBQXVEO1FBQ3hILElBQUk7WUFDRixNQUFNVCxXQUFXSCxtRUFBb0JBO1lBRXJDLDRCQUE0QjtZQUM1QixNQUFNYSxnQkFBZ0I7Z0JBQ3BCQyxJQUFJSCxhQUFhRyxFQUFFO2dCQUNuQkMsT0FBT0osYUFBYUksS0FBSztnQkFDekJDLGNBQWNKLGVBQWVELGFBQWFNLGFBQWEsRUFBRUQsZ0JBQWdCO2dCQUN6RUUsV0FBV1AsYUFBYU0sYUFBYSxFQUFFRTtnQkFDdkNDLE1BQU07Z0JBQ05DLGFBQWE7b0JBQ1hDLE9BQU87b0JBQ1BDLGVBQWU7Z0JBQ2pCO1lBQ0Y7WUFFQSxNQUFNLEVBQUVDLE1BQU1DLFNBQVMsRUFBRXBCLE9BQU9xQixTQUFTLEVBQUUsR0FBRyxNQUFNdkIsU0FDakRHLElBQUksQ0FBQyxpQkFDTHFCLE1BQU0sQ0FBQztnQkFBQ2Q7YUFBYyxFQUN0Qk4sTUFBTSxHQUNOcUIsTUFBTTtZQUVULElBQUlGLFdBQVcsTUFBTUE7WUFFckIsOEJBQThCO1lBQzlCLE1BQU1HLFVBQVU7Z0JBQ2RDLE1BQU0sQ0FBQyxFQUFFakIsY0FBY0csWUFBWSxDQUFDLGVBQWUsQ0FBQztnQkFDcERlLGFBQWE7Z0JBQ2JDLFVBQVVyQixhQUFhRyxFQUFFO2dCQUN6Qm1CLFVBQVU7b0JBQ1JDLFVBQVU7d0JBQ1IsNEJBQTRCOzRCQUMxQkMsU0FBUzs0QkFDVHZCLGFBQWE7NEJBQ2J3QixVQUFVO3dCQUNaO3dCQUNBLDZCQUE2Qjs0QkFDM0JELFNBQVM7NEJBQ1R2QixhQUFhOzRCQUNid0IsVUFBVTt3QkFDWjt3QkFDQSxnQkFBZ0I7NEJBQ2RELFNBQVM7NEJBQ1R2QixhQUFhOzRCQUNid0IsVUFBVTt3QkFDWjt3QkFDQSx3QkFBd0I7NEJBQ3RCRCxTQUFTOzRCQUNUdkIsYUFBYTs0QkFDYndCLFVBQVU7d0JBQ1o7b0JBQ0Y7b0JBQ0FDLFlBQVk7Z0JBQ2Q7Z0JBQ0FDLFVBQVU7b0JBQ1JDLFlBQVksRUFBRTtnQkFDaEI7WUFDRjtZQUVBLE1BQU0sRUFBRWYsTUFBTWdCLFlBQVksRUFBRW5DLE9BQU9vQyxRQUFRLEVBQUUsR0FBRyxNQUFNdEMsU0FDbkRHLElBQUksQ0FBQyxpQkFDTHFCLE1BQU0sQ0FBQztnQkFBQ0U7YUFBUSxFQUNoQnRCLE1BQU0sR0FDTnFCLE1BQU07WUFFVCxJQUFJYSxVQUFVLE1BQU1BO1lBRXBCLG1DQUFtQztZQUNuQyxNQUFNLEVBQUVqQixNQUFNa0IsV0FBVyxFQUFFckMsT0FBT3NDLFdBQVcsRUFBRSxHQUFHLE1BQU14QyxTQUNyREcsSUFBSSxDQUFDLGlCQUNMc0MsTUFBTSxDQUFDO2dCQUFFQyxpQkFBaUJMLGFBQWExQixFQUFFO1lBQUMsR0FDMUNnQyxFQUFFLENBQUMsTUFBTW5DLGFBQWFHLEVBQUUsRUFDeEJQLE1BQU0sR0FDTnFCLE1BQU07WUFFVCxJQUFJZSxhQUFhLE1BQU1BO1lBRXZCLE9BQU87Z0JBQUVJLE1BQU1MO2dCQUFxQkYsY0FBY0E7WUFBNkI7UUFDakYsRUFBRSxPQUFPbkMsT0FBWTtZQUNuQkksUUFBUUosS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsTUFBTSxJQUFJMkMsTUFBTSxDQUFDLDZCQUE2QixFQUFFM0MsTUFBTTRDLE9BQU8sQ0FBQyxDQUFDO1FBQ2pFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFDLHNCQUF3QztRQUNuRCxJQUFJO1lBQ0YsTUFBTS9DLFdBQVdILG1FQUFvQkE7WUFDckMsTUFBTSxFQUFFSSxLQUFLLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1GLFNBQzVCRyxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxLQUFLO2dCQUFFSCxPQUFPO2dCQUFTSSxNQUFNO1lBQUs7WUFFNUMsSUFBSUgsT0FBTztnQkFDVEksUUFBUUosS0FBSyxDQUFDLHlDQUF5Q0E7Z0JBQ3ZELE9BQU87WUFDVDtZQUVBLE9BQU8sQ0FBQ0QsU0FBUyxLQUFLO1FBQ3hCLEVBQUUsT0FBT0MsT0FBTztZQUNkSSxRQUFRSixLQUFLLENBQUMseUNBQXlDQTtZQUN2RCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYThDLHlCQUF1RDtRQUNsRSxJQUFJO1lBQ0YsTUFBTWhELFdBQVdILG1FQUFvQkE7WUFDckMsTUFBTSxFQUFFd0IsSUFBSSxFQUFFbkIsS0FBSyxFQUFFLEdBQUcsTUFBTUYsU0FDM0JHLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1A2QyxLQUFLLENBQUMsR0FDTnhCLE1BQU07WUFFVCxJQUFJdkIsT0FBTztnQkFDVCxJQUFJQSxNQUFNZ0QsSUFBSSxLQUFLLFlBQVk7b0JBQzdCLE9BQU8sS0FBSyx5QkFBeUI7O2dCQUN2QztnQkFDQTVDLFFBQVFKLEtBQUssQ0FBQyx1Q0FBdUNBO2dCQUNyRCxPQUFPO1lBQ1Q7WUFFQSxPQUFPbUI7UUFDVCxFQUFFLE9BQU9uQixPQUFPO1lBQ2RJLFFBQVFKLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhaUQsa0JBQWtCM0MsWUFBaUIsRUFBRUMsV0FBbUIsRUFBaUI7UUFDcEYsSUFBSTtZQUNGLE1BQU1ULFdBQVdILG1FQUFvQkE7WUFFckMsK0JBQStCO1lBQy9CLE1BQU11RCxhQUFhLE1BQU0sSUFBSSxDQUFDSixzQkFBc0I7WUFFcEQsTUFBTUssV0FBVztnQkFDZjFDLElBQUlILGFBQWFHLEVBQUU7Z0JBQ25CQyxPQUFPSixhQUFhSSxLQUFLO2dCQUN6QkMsY0FBY0osZUFBZUQsYUFBYU0sYUFBYSxFQUFFRCxnQkFBZ0I7Z0JBQ3pFRSxXQUFXUCxhQUFhTSxhQUFhLEVBQUVFO2dCQUN2Q0MsTUFBTTtnQkFDTnlCLGlCQUFpQlUsWUFBWXpDO2dCQUM3Qk8sYUFBYTtvQkFDWEMsT0FBTztvQkFDUEMsZUFBZTtnQkFDakI7WUFDRjtZQUVBLE1BQU0sRUFBRUMsTUFBTXVCLElBQUksRUFBRTFDLEtBQUssRUFBRSxHQUFHLE1BQU1GLFNBQ2pDRyxJQUFJLENBQUMsaUJBQ0xxQixNQUFNLENBQUM7Z0JBQUM2QjthQUFTLEVBQ2pCakQsTUFBTSxHQUNOcUIsTUFBTTtZQUVULElBQUl2QixPQUFPLE1BQU1BO1lBRWpCLE9BQU8wQztRQUNULEVBQUUsT0FBTzFDLE9BQVk7WUFDbkJJLFFBQVFKLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLE1BQU0sSUFBSTJDLE1BQU0sQ0FBQyx1QkFBdUIsRUFBRTNDLE1BQU00QyxPQUFPLENBQUMsQ0FBQztRQUMzRDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcm9kdWN0aXZpdHktcGxhdGZvcm0vLi9zcmMvbGliL3NlcnZpY2VzL3NldHVwU2VydmljZS50cz9mZTk4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnXHJcbmltcG9ydCB7IFVzZXIsIE9yZ2FuaXphdGlvbiB9IGZyb20gJ0AvbGliL3R5cGVzJ1xyXG5cclxuZXhwb3J0IGNsYXNzIFNldHVwU2VydmljZSB7XHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgdGhpcyBpcyB0aGUgZmlyc3QgdXNlciAoYWRtaW4pIGluIHRoZSBzeXN0ZW1cclxuICAgKi9cclxuICBzdGF0aWMgYXN5bmMgaXNGaXJzdFVzZXIoKTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVN1cGFiYXNlQ2xpZW50KClcclxuICAgICAgY29uc3QgeyBjb3VudCwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxyXG4gICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXHJcblxyXG4gICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBpZiBmaXJzdCB1c2VyOicsIGVycm9yKVxyXG4gICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gY291bnQgPT09IDBcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIGlmIGZpcnN0IHVzZXI6JywgZXJyb3IpXHJcbiAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ3JlYXRlIHRoZSBhZG1pbiB1c2VyIGFuZCBkZWZhdWx0IG9yZ2FuaXphdGlvblxyXG4gICAqL1xyXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVBZG1pblVzZXIoc3VwYWJhc2VVc2VyOiBhbnksIGRpc3BsYXlOYW1lOiBzdHJpbmcpOiBQcm9taXNlPHsgdXNlcjogVXNlcjsgb3JnYW5pemF0aW9uOiBPcmdhbml6YXRpb24gfT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZUNsaWVudCgpXHJcblxyXG4gICAgICAvLyBDcmVhdGUgYWRtaW4gdXNlciBwcm9maWxlXHJcbiAgICAgIGNvbnN0IGFkbWluVXNlckRhdGEgPSB7XHJcbiAgICAgICAgaWQ6IHN1cGFiYXNlVXNlci5pZCxcclxuICAgICAgICBlbWFpbDogc3VwYWJhc2VVc2VyLmVtYWlsLFxyXG4gICAgICAgIGRpc3BsYXlfbmFtZTogZGlzcGxheU5hbWUgfHwgc3VwYWJhc2VVc2VyLnVzZXJfbWV0YWRhdGE/LmRpc3BsYXlfbmFtZSB8fCAnQWRtaW4nLFxyXG4gICAgICAgIHBob3RvX3VybDogc3VwYWJhc2VVc2VyLnVzZXJfbWV0YWRhdGE/LmF2YXRhcl91cmwsXHJcbiAgICAgICAgcm9sZTogJ2FkbWluJyBhcyBjb25zdCxcclxuICAgICAgICBwcmVmZXJlbmNlczoge1xyXG4gICAgICAgICAgdGhlbWU6ICdsaWdodCcgYXMgY29uc3QsXHJcbiAgICAgICAgICBub3RpZmljYXRpb25zOiB0cnVlXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB7IGRhdGE6IGFkbWluVXNlciwgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXHJcbiAgICAgICAgLmluc2VydChbYWRtaW5Vc2VyRGF0YV0pXHJcbiAgICAgICAgLnNlbGVjdCgpXHJcbiAgICAgICAgLnNpbmdsZSgpXHJcblxyXG4gICAgICBpZiAodXNlckVycm9yKSB0aHJvdyB1c2VyRXJyb3JcclxuXHJcbiAgICAgIC8vIENyZWF0ZSBkZWZhdWx0IG9yZ2FuaXphdGlvblxyXG4gICAgICBjb25zdCBvcmdEYXRhID0ge1xyXG4gICAgICAgIG5hbWU6IGAke2FkbWluVXNlckRhdGEuZGlzcGxheV9uYW1lfSdzIE9yZ2FuaXphdGlvbmAsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdEZWZhdWx0IG9yZ2FuaXphdGlvbiBmb3IgdGhlIGFkbWluIHVzZXInLFxyXG4gICAgICAgIG93bmVyX2lkOiBzdXBhYmFzZVVzZXIuaWQsXHJcbiAgICAgICAgc2V0dGluZ3M6IHtcclxuICAgICAgICAgIGFpTW9kZWxzOiB7XHJcbiAgICAgICAgICAgICdhbnRocm9waWMvY2xhdWRlLTMtaGFpa3UnOiB7XHJcbiAgICAgICAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICBkaXNwbGF5TmFtZTogJ0NsYXVkZSAzIEhhaWt1IChGYXN0KScsXHJcbiAgICAgICAgICAgICAgcHJvdmlkZXI6ICdvcGVucm91dGVyJ1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAnYW50aHJvcGljL2NsYXVkZS0zLXNvbm5ldCc6IHtcclxuICAgICAgICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGRpc3BsYXlOYW1lOiAnQ2xhdWRlIDMgU29ubmV0IChCYWxhbmNlZCknLFxyXG4gICAgICAgICAgICAgIHByb3ZpZGVyOiAnb3BlbnJvdXRlcidcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgJ29wZW5haS9ncHQtNCc6IHtcclxuICAgICAgICAgICAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGRpc3BsYXlOYW1lOiAnR1BULTQgKEFkdmFuY2VkKScsXHJcbiAgICAgICAgICAgICAgcHJvdmlkZXI6ICdvcGVucm91dGVyJ1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAnb3BlbmFpL2dwdC0zLjUtdHVyYm8nOiB7XHJcbiAgICAgICAgICAgICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICBkaXNwbGF5TmFtZTogJ0dQVC0zLjUgVHVyYm8gKEZhc3QpJyxcclxuICAgICAgICAgICAgICBwcm92aWRlcjogJ29wZW5yb3V0ZXInXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBtY3BFbmFibGVkOiBmYWxzZVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYXBpX2tleXM6IHtcclxuICAgICAgICAgIG9wZW5yb3V0ZXI6IFtdXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB7IGRhdGE6IG9yZ2FuaXphdGlvbiwgZXJyb3I6IG9yZ0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKCdvcmdhbml6YXRpb25zJylcclxuICAgICAgICAuaW5zZXJ0KFtvcmdEYXRhXSlcclxuICAgICAgICAuc2VsZWN0KClcclxuICAgICAgICAuc2luZ2xlKClcclxuXHJcbiAgICAgIGlmIChvcmdFcnJvcikgdGhyb3cgb3JnRXJyb3JcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB1c2VyIHdpdGggb3JnYW5pemF0aW9uX2lkXHJcbiAgICAgIGNvbnN0IHsgZGF0YTogdXBkYXRlZFVzZXIsIGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXHJcbiAgICAgICAgLnVwZGF0ZSh7IG9yZ2FuaXphdGlvbl9pZDogb3JnYW5pemF0aW9uLmlkIH0pXHJcbiAgICAgICAgLmVxKCdpZCcsIHN1cGFiYXNlVXNlci5pZClcclxuICAgICAgICAuc2VsZWN0KClcclxuICAgICAgICAuc2luZ2xlKClcclxuXHJcbiAgICAgIGlmICh1cGRhdGVFcnJvcikgdGhyb3cgdXBkYXRlRXJyb3JcclxuXHJcbiAgICAgIHJldHVybiB7IHVzZXI6IHVwZGF0ZWRVc2VyIGFzIFVzZXIsIG9yZ2FuaXphdGlvbjogb3JnYW5pemF0aW9uIGFzIE9yZ2FuaXphdGlvbiB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGFkbWluIHVzZXI6JywgZXJyb3IpXHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBhZG1pbiB1c2VyOiAke2Vycm9yLm1lc3NhZ2V9YClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHRoZSBzeXN0ZW0gaGFzIGJlZW4gaW5pdGlhbGl6ZWRcclxuICAgKi9cclxuICBzdGF0aWMgYXN5bmMgaXNTeXN0ZW1Jbml0aWFsaXplZCgpOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU3VwYWJhc2VDbGllbnQoKVxyXG4gICAgICBjb25zdCB7IGNvdW50LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbSgnb3JnYW5pemF0aW9ucycpXHJcbiAgICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcclxuXHJcbiAgICAgIGlmIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIHN5c3RlbSBpbml0aWFsaXphdGlvbjonLCBlcnJvcilcclxuICAgICAgICByZXR1cm4gZmFsc2VcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIChjb3VudCB8fCAwKSA+IDBcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIHN5c3RlbSBpbml0aWFsaXphdGlvbjonLCBlcnJvcilcclxuICAgICAgcmV0dXJuIGZhbHNlXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdGhlIGZpcnN0IG9yZ2FuaXphdGlvbiAoZGVmYXVsdCBvcmdhbml6YXRpb24pXHJcbiAgICovXHJcbiAgc3RhdGljIGFzeW5jIGdldERlZmF1bHRPcmdhbml6YXRpb24oKTogUHJvbWlzZTxPcmdhbml6YXRpb24gfCBudWxsPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVN1cGFiYXNlQ2xpZW50KClcclxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbSgnb3JnYW5pemF0aW9ucycpXHJcbiAgICAgICAgLnNlbGVjdCgnKicpXHJcbiAgICAgICAgLmxpbWl0KDEpXHJcbiAgICAgICAgLnNpbmdsZSgpXHJcblxyXG4gICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBpZiAoZXJyb3IuY29kZSA9PT0gJ1BHUlNUMTE2Jykge1xyXG4gICAgICAgICAgcmV0dXJuIG51bGwgLy8gTm8gb3JnYW5pemF0aW9ucyBmb3VuZFxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGRlZmF1bHQgb3JnYW5pemF0aW9uOicsIGVycm9yKVxyXG4gICAgICAgIHJldHVybiBudWxsXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBkYXRhIGFzIE9yZ2FuaXphdGlvblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBkZWZhdWx0IG9yZ2FuaXphdGlvbjonLCBlcnJvcilcclxuICAgICAgcmV0dXJuIG51bGxcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENyZWF0ZSBhIHJlZ3VsYXIgdXNlciAobm9uLWFkbWluKVxyXG4gICAqL1xyXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVSZWd1bGFyVXNlcihzdXBhYmFzZVVzZXI6IGFueSwgZGlzcGxheU5hbWU6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZUNsaWVudCgpXHJcblxyXG4gICAgICAvLyBHZXQgdGhlIGRlZmF1bHQgb3JnYW5pemF0aW9uXHJcbiAgICAgIGNvbnN0IGRlZmF1bHRPcmcgPSBhd2FpdCB0aGlzLmdldERlZmF1bHRPcmdhbml6YXRpb24oKVxyXG5cclxuICAgICAgY29uc3QgdXNlckRhdGEgPSB7XHJcbiAgICAgICAgaWQ6IHN1cGFiYXNlVXNlci5pZCxcclxuICAgICAgICBlbWFpbDogc3VwYWJhc2VVc2VyLmVtYWlsLFxyXG4gICAgICAgIGRpc3BsYXlfbmFtZTogZGlzcGxheU5hbWUgfHwgc3VwYWJhc2VVc2VyLnVzZXJfbWV0YWRhdGE/LmRpc3BsYXlfbmFtZSB8fCAnJyxcclxuICAgICAgICBwaG90b191cmw6IHN1cGFiYXNlVXNlci51c2VyX21ldGFkYXRhPy5hdmF0YXJfdXJsLFxyXG4gICAgICAgIHJvbGU6ICd1c2VyJyBhcyBjb25zdCxcclxuICAgICAgICBvcmdhbml6YXRpb25faWQ6IGRlZmF1bHRPcmc/LmlkLFxyXG4gICAgICAgIHByZWZlcmVuY2VzOiB7XHJcbiAgICAgICAgICB0aGVtZTogJ2xpZ2h0JyBhcyBjb25zdCxcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbnM6IHRydWVcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHsgZGF0YTogdXNlciwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxyXG4gICAgICAgIC5pbnNlcnQoW3VzZXJEYXRhXSlcclxuICAgICAgICAuc2VsZWN0KClcclxuICAgICAgICAuc2luZ2xlKClcclxuXHJcbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcclxuXHJcbiAgICAgIHJldHVybiB1c2VyIGFzIFVzZXJcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgcmVndWxhciB1c2VyOicsIGVycm9yKVxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgdXNlcjogJHtlcnJvci5tZXNzYWdlfWApXHJcbiAgICB9XHJcbiAgfVxyXG59Il0sIm5hbWVzIjpbImNyZWF0ZVN1cGFiYXNlQ2xpZW50IiwiU2V0dXBTZXJ2aWNlIiwiaXNGaXJzdFVzZXIiLCJzdXBhYmFzZSIsImNvdW50IiwiZXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiaGVhZCIsImNvbnNvbGUiLCJjcmVhdGVBZG1pblVzZXIiLCJzdXBhYmFzZVVzZXIiLCJkaXNwbGF5TmFtZSIsImFkbWluVXNlckRhdGEiLCJpZCIsImVtYWlsIiwiZGlzcGxheV9uYW1lIiwidXNlcl9tZXRhZGF0YSIsInBob3RvX3VybCIsImF2YXRhcl91cmwiLCJyb2xlIiwicHJlZmVyZW5jZXMiLCJ0aGVtZSIsIm5vdGlmaWNhdGlvbnMiLCJkYXRhIiwiYWRtaW5Vc2VyIiwidXNlckVycm9yIiwiaW5zZXJ0Iiwic2luZ2xlIiwib3JnRGF0YSIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsIm93bmVyX2lkIiwic2V0dGluZ3MiLCJhaU1vZGVscyIsImVuYWJsZWQiLCJwcm92aWRlciIsIm1jcEVuYWJsZWQiLCJhcGlfa2V5cyIsIm9wZW5yb3V0ZXIiLCJvcmdhbml6YXRpb24iLCJvcmdFcnJvciIsInVwZGF0ZWRVc2VyIiwidXBkYXRlRXJyb3IiLCJ1cGRhdGUiLCJvcmdhbml6YXRpb25faWQiLCJlcSIsInVzZXIiLCJFcnJvciIsIm1lc3NhZ2UiLCJpc1N5c3RlbUluaXRpYWxpemVkIiwiZ2V0RGVmYXVsdE9yZ2FuaXphdGlvbiIsImxpbWl0IiwiY29kZSIsImNyZWF0ZVJlZ3VsYXJVc2VyIiwiZGVmYXVsdE9yZyIsInVzZXJEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/services/setupService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useBoardStore: () => (/* binding */ useBoardStore),\n/* harmony export */   useChatStore: () => (/* binding */ useChatStore),\n/* harmony export */   useOrganizationStore: () => (/* binding */ useOrganizationStore),\n/* harmony export */   useProjectStore: () => (/* binding */ useProjectStore),\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        user: null,\n        loading: true,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            })\n    }));\nconst useOrganizationStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentOrganization: null,\n        organizations: [],\n        setCurrentOrganization: (org)=>set({\n                currentOrganization: org\n            }),\n        setOrganizations: (orgs)=>set({\n                organizations: orgs\n            }),\n        addOrganization: (org)=>set({\n                organizations: [\n                    ...get().organizations,\n                    org\n                ]\n            }),\n        updateOrganization: (org)=>set({\n                organizations: get().organizations.map((o)=>o.id === org.id ? org : o),\n                currentOrganization: get().currentOrganization?.id === org.id ? org : get().currentOrganization\n            })\n    }));\nconst useProjectStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentProject: null,\n        projects: [],\n        setCurrentProject: (project)=>set({\n                currentProject: project\n            }),\n        setProjects: (projects)=>set({\n                projects\n            }),\n        addProject: (project)=>set({\n                projects: [\n                    ...get().projects,\n                    project\n                ]\n            }),\n        updateProject: (project)=>set({\n                projects: get().projects.map((p)=>p.id === project.id ? project : p),\n                currentProject: get().currentProject?.id === project.id ? project : get().currentProject\n            })\n    }));\nconst useBoardStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentBoard: null,\n        boards: [],\n        setCurrentBoard: (board)=>set({\n                currentBoard: board\n            }),\n        setBoards: (boards)=>set({\n                boards\n            }),\n        addBoard: (board)=>set({\n                boards: [\n                    ...get().boards,\n                    board\n                ]\n            }),\n        updateBoard: (board)=>set({\n                boards: get().boards.map((b)=>b.id === board.id ? board : b),\n                currentBoard: get().currentBoard?.id === board.id ? board : get().currentBoard\n            })\n    }));\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        selectedTask: null,\n        setTasks: (tasks)=>set({\n                tasks\n            }),\n        setSelectedTask: (task)=>set({\n                selectedTask: task\n            }),\n        addTask: (task)=>set({\n                tasks: [\n                    ...get().tasks,\n                    task\n                ]\n            }),\n        updateTask: (task)=>set({\n                tasks: get().tasks.map((t)=>t.id === task.id ? task : t),\n                selectedTask: get().selectedTask?.id === task.id ? task : get().selectedTask\n            }),\n        removeTask: (taskId)=>set({\n                tasks: get().tasks.filter((t)=>t.id !== taskId),\n                selectedTask: get().selectedTask?.id === taskId ? null : get().selectedTask\n            })\n    }));\nconst useChatStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentSession: null,\n        sessions: [],\n        isLoading: false,\n        setCurrentSession: (session)=>set({\n                currentSession: session\n            }),\n        setSessions: (sessions)=>set({\n                sessions\n            }),\n        addSession: (session)=>set({\n                sessions: [\n                    ...get().sessions,\n                    session\n                ]\n            }),\n        updateSession: (session)=>set({\n                sessions: get().sessions.map((s)=>s.id === session.id ? session : s),\n                currentSession: get().currentSession?.id === session.id ? session : get().currentSession\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        sidebarOpen: true,\n        chatPanelOpen: false,\n        taskModalOpen: false,\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        setChatPanelOpen: (open)=>set({\n                chatPanelOpen: open\n            }),\n        setTaskModalOpen: (open)=>set({\n                taskModalOpen: open\n            }),\n        toggleSidebar: ()=>set({\n                sidebarOpen: !get().sidebarOpen\n            }),\n        toggleChatPanel: ()=>set({\n                chatPanelOpen: !get().chatPanelOpen\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdminClient: () => (/* binding */ createSupabaseAdminClient),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://qwgffjjkcicexwqnhvqw.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3Z2ZmamprY2ljZXh3cW5odnF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2ODgyNjAsImV4cCI6MjA2MDI2NDI2MH0.UCq6jp8EPCvp3cmuvR5bKVykebpP6AWIjsRB6AZPuZU\";\n// Client for use in client components\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client component helper\nconst createSupabaseClient = ()=>(0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Admin client for server-side operations\nconst createSupabaseAdminClient = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"49997f4810d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kNzM0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDk5OTdmNDgxMGQ0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/AuthProvider */ \"(rsc)/./src/components/auth/AuthProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"AI Productivity Platform\",\n    description: \"Modern productivity platform with AI superpowers\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ21DO0FBSWxELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MscUVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJvZHVjdGl2aXR5LXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcclxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgQXV0aFByb3ZpZGVyIGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL0F1dGhQcm92aWRlcidcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdBSSBQcm9kdWN0aXZpdHkgUGxhdGZvcm0nLFxyXG4gIGRlc2NyaXB0aW9uOiAnTW9kZXJuIHByb2R1Y3Rpdml0eSBwbGF0Zm9ybSB3aXRoIEFJIHN1cGVycG93ZXJzJyxcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cclxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59Il0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/auth/AuthProvider.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/AuthProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\code-files\ai files\to-do\src\components\auth\AuthProvider.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/use-sync-external-store","vendor-chunks/webidl-conversions","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccode-files%5Cai%20files%5Cto-do&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();